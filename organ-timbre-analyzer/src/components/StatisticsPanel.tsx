'use client';

import { useState, useEffect } from 'react';

interface StatisticalSummary {
  total_analyses: number;
  historical_count: number;
  modern_count: number;
  restored_count: number;
  avg_brightness_historical: number;
  avg_brightness_modern: number;
  avg_brightness_restored: number;
  avg_warmth_historical: number;
  avg_warmth_modern: number;
  avg_warmth_restored: number;
  significant_differences: any[];
}

export default function StatisticsPanel() {
  const [stats, setStats] = useState<StatisticalSummary | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchStatistics();
  }, []);

  const fetchStatistics = async () => {
    try {
      const response = await fetch('http://localhost:8000/statistics');
      if (response.ok) {
        const data = await response.json();
        setStats(data);
      }
    } catch (error) {
      console.error('Failed to fetch statistics:', error);
      // Set placeholder data for demo
      setStats({
        total_analyses: 0,
        historical_count: 0,
        modern_count: 0,
        restored_count: 0,
        avg_brightness_historical: 0,
        avg_brightness_modern: 0,
        avg_brightness_restored: 0,
        avg_warmth_historical: 0,
        avg_warmth_modern: 0,
        avg_warmth_restored: 0,
        significant_differences: []
      });
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded mb-4"></div>
          <div className="space-y-3">
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Database Statistics */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
        <h3 className="text-xl font-bold text-gray-800 dark:text-white mb-4">
          Database Statistics
        </h3>
        
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <span className="text-gray-600 dark:text-gray-400">Total Analyses</span>
            <span className="text-2xl font-bold text-gray-900 dark:text-white">
              {stats?.total_analyses || 0}
            </span>
          </div>
          
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600 dark:text-gray-400">Historical Organs</span>
              <span className="font-medium text-amber-600">
                {stats?.historical_count || 0}
              </span>
            </div>
            
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600 dark:text-gray-400">Restored Organs</span>
              <span className="font-medium text-blue-600">
                {stats?.restored_count || 0}
              </span>
            </div>
            
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600 dark:text-gray-400">Modern Organs</span>
              <span className="font-medium text-green-600">
                {stats?.modern_count || 0}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Research Insights */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
        <h3 className="text-xl font-bold text-gray-800 dark:text-white mb-4">
          Research Insights
        </h3>
        
        {stats && stats.total_analyses > 0 ? (
          <div className="space-y-4">
            {/* Temperament Distribution */}
            <div>
              <h4 className="font-medium text-gray-700 dark:text-gray-300 mb-2">
                Temperament Distribution
              </h4>
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-purple-600">Equal Temperament</span>
                  <span className="font-medium">
                    {stats.temperament_distribution?.equal?.percentage?.toFixed(1) || 0}%
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-orange-600">Just Intonation</span>
                  <span className="font-medium">
                    {stats.temperament_distribution?.just?.percentage?.toFixed(1) || 0}%
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-red-600">Meantone</span>
                  <span className="font-medium">
                    {stats.temperament_distribution?.meantone?.percentage?.toFixed(1) || 0}%
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-indigo-600">Well Tempered</span>
                  <span className="font-medium">
                    {stats.temperament_distribution?.well_tempered?.percentage?.toFixed(1) || 0}%
                  </span>
                </div>
              </div>
            </div>

            {/* Tuning Reference Distribution */}
            <div>
              <h4 className="font-medium text-gray-700 dark:text-gray-300 mb-2">
                Tuning Reference Distribution
              </h4>
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-blue-600">A440 (Modern)</span>
                  <span className="font-medium">
                    {stats.tuning_distribution?.A440?.percentage?.toFixed(1) || 0}%
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-green-600">A432 (Alternative)</span>
                  <span className="font-medium">
                    {stats.tuning_distribution?.A432?.percentage?.toFixed(1) || 0}%
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-amber-600">A415 (Baroque)</span>
                  <span className="font-medium">
                    {stats.tuning_distribution?.A415_baroque?.percentage?.toFixed(1) || 0}%
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-red-600">A466 (High Baroque)</span>
                  <span className="font-medium">
                    {stats.tuning_distribution?.A466_high_baroque?.percentage?.toFixed(1) || 0}%
                  </span>
                </div>
              </div>
            </div>

            {/* Key Research Findings */}
            {stats.key_findings && stats.key_findings.length > 0 && (
              <div>
                <h4 className="font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Key Research Findings
                </h4>
                <div className="space-y-1">
                  {stats.key_findings.map((finding: string, index: number) => (
                    <p key={index} className="text-xs text-gray-600 dark:text-gray-400">
                      • {finding}
                    </p>
                  ))}
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="text-center py-8">
            <div className="text-gray-400 dark:text-gray-500 mb-2">
              <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
            <p className="text-gray-500 dark:text-gray-400 text-sm">
              No analyses yet. Start analyzing organs to see research insights!
            </p>
          </div>
        )}
      </div>

      {/* Quick Actions */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
        <h3 className="text-xl font-bold text-gray-800 dark:text-white mb-4">
          Quick Actions
        </h3>
        
        <div className="space-y-3">
          <button
            onClick={async () => {
              try {
                const response = await fetch('http://localhost:8000/discover-batch', {
                  method: 'POST',
                  headers: { 'Content-Type': 'application/json' },
                  body: JSON.stringify({ batch_size: 5 })
                });
                if (response.ok) {
                  const result = await response.json();
                  alert(`Discovered ${result.count} new organ videos!`);
                  fetchStatistics(); // Refresh stats
                }
              } catch (error) {
                console.error('Discovery failed:', error);
              }
            }}
            className="w-full text-left p-3 rounded-lg border border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
          >
            <div className="font-medium text-gray-900 dark:text-white">Auto-Discover Videos</div>
            <div className="text-sm text-gray-600 dark:text-gray-400">
              Find and analyze new organ recordings
            </div>
          </button>

          <button className="w-full text-left p-3 rounded-lg border border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
            <div className="font-medium text-gray-900 dark:text-white">Compare Analyses</div>
            <div className="text-sm text-gray-600 dark:text-gray-400">
              Compare two organ recordings
            </div>
          </button>

          <button className="w-full text-left p-3 rounded-lg border border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
            <div className="font-medium text-gray-900 dark:text-white">Export Data</div>
            <div className="text-sm text-gray-600 dark:text-gray-400">
              Download analysis results
            </div>
          </button>

          <button className="w-full text-left p-3 rounded-lg border border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
            <div className="font-medium text-gray-900 dark:text-white">View All Analyses</div>
            <div className="text-sm text-gray-600 dark:text-gray-400">
              Browse analysis history
            </div>
          </button>
        </div>
      </div>

      {/* Research Tips */}
      <div className="bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-lg p-6">
        <h3 className="text-lg font-bold text-purple-800 dark:text-purple-200 mb-3">
          Research Tips
        </h3>
        
        <div className="space-y-2 text-sm text-purple-700 dark:text-purple-300">
          <p>• Analyze multiple recordings from the same organ for consistency</p>
          <p>• Compare organs from similar time periods and regions</p>
          <p>• Look for patterns in restoration vs. original recordings</p>
          <p>• Consider room acoustics when interpreting results</p>
        </div>
      </div>
    </div>
  );
}
