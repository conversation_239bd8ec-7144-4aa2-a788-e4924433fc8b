{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/youtube_spider_analyzer/organ-timbre-analyzer/src/components/SearchForm.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\n\ninterface SearchFormProps {\n  onAnalysisStart: () => void;\n  onAnalysisComplete: (result: any) => void;\n  onAnalysisError: () => void;\n  isAnalyzing: boolean;\n}\n\nexport default function SearchForm({ \n  onAnalysisStart, \n  onAnalysisComplete, \n  onAnalysisError, \n  isAnalyzing \n}: SearchFormProps) {\n  const [youtubeUrl, setYoutubeUrl] = useState('');\n  const [organType, setOrganType] = useState('historical');\n  const [organAge, setOrganAge] = useState('');\n  const [restorationDate, setRestorationDate] = useState('');\n  const [location, setLocation] = useState('');\n  const [churchName, setChurchName] = useState('');\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!youtubeUrl.trim()) {\n      alert('Please enter a YouTube URL');\n      return;\n    }\n\n    onAnalysisStart();\n\n    try {\n      const response = await fetch('http://localhost:8000/analyze', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          youtube_url: youtubeUrl,\n          organ_type: organType,\n          organ_age: organAge ? parseInt(organAge) : null,\n          restoration_date: restorationDate ? parseInt(restorationDate) : null,\n          location: location || null,\n          church_name: churchName || null,\n        }),\n      });\n\n      if (!response.ok) {\n        throw new Error('Analysis failed');\n      }\n\n      const result = await response.json();\n      onAnalysisComplete(result);\n    } catch (error) {\n      console.error('Analysis error:', error);\n      alert('Analysis failed. Please check the YouTube URL and try again.');\n      onAnalysisError();\n    }\n  };\n\n  return (\n    <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\">\n      <h2 className=\"text-2xl font-bold text-gray-800 dark:text-white mb-6\">\n        Analyze Church Organ Recording\n      </h2>\n      \n      <form onSubmit={handleSubmit} className=\"space-y-6\">\n        {/* YouTube URL */}\n        <div>\n          <label htmlFor=\"youtube-url\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n            YouTube URL *\n          </label>\n          <input\n            type=\"url\"\n            id=\"youtube-url\"\n            value={youtubeUrl}\n            onChange={(e) => setYoutubeUrl(e.target.value)}\n            placeholder=\"https://www.youtube.com/watch?v=...\"\n            className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white\"\n            required\n            disabled={isAnalyzing}\n          />\n        </div>\n\n        {/* Organ Type */}\n        <div>\n          <label htmlFor=\"organ-type\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n            Organ Type *\n          </label>\n          <select\n            id=\"organ-type\"\n            value={organType}\n            onChange={(e) => setOrganType(e.target.value)}\n            className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white\"\n            disabled={isAnalyzing}\n          >\n            <option value=\"historical\">Historical (Original/Unrestored)</option>\n            <option value=\"restored\">Restored</option>\n            <option value=\"modern\">Modern (Built after 1950)</option>\n          </select>\n        </div>\n\n        {/* Additional Information */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          <div>\n            <label htmlFor=\"organ-age\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Original Build Year\n            </label>\n            <input\n              type=\"number\"\n              id=\"organ-age\"\n              value={organAge}\n              onChange={(e) => setOrganAge(e.target.value)}\n              placeholder=\"e.g., 1750\"\n              min=\"1400\"\n              max=\"2024\"\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white\"\n              disabled={isAnalyzing}\n            />\n          </div>\n\n          <div>\n            <label htmlFor=\"restoration-date\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Restoration Year\n            </label>\n            <input\n              type=\"number\"\n              id=\"restoration-date\"\n              value={restorationDate}\n              onChange={(e) => setRestorationDate(e.target.value)}\n              placeholder=\"e.g., 2010\"\n              min=\"1900\"\n              max=\"2024\"\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white\"\n              disabled={isAnalyzing}\n            />\n          </div>\n        </div>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          <div>\n            <label htmlFor=\"church-name\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Church Name\n            </label>\n            <input\n              type=\"text\"\n              id=\"church-name\"\n              value={churchName}\n              onChange={(e) => setChurchName(e.target.value)}\n              placeholder=\"e.g., St. Mary's Cathedral\"\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white\"\n              disabled={isAnalyzing}\n            />\n          </div>\n\n          <div>\n            <label htmlFor=\"location\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Location\n            </label>\n            <input\n              type=\"text\"\n              id=\"location\"\n              value={location}\n              onChange={(e) => setLocation(e.target.value)}\n              placeholder=\"e.g., Vienna, Austria\"\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white\"\n              disabled={isAnalyzing}\n            />\n          </div>\n        </div>\n\n        {/* Submit Button */}\n        <button\n          type=\"submit\"\n          disabled={isAnalyzing}\n          className=\"w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-medium py-3 px-4 rounded-md transition-colors duration-200 flex items-center justify-center\"\n        >\n          {isAnalyzing ? (\n            <>\n              <svg className=\"animate-spin -ml-1 mr-3 h-5 w-5 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n                <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n                <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n              </svg>\n              Analyzing Audio...\n            </>\n          ) : (\n            'Analyze Organ Timbre'\n          )}\n        </button>\n      </form>\n\n      {/* Help Text */}\n      <div className=\"mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-md\">\n        <h3 className=\"text-sm font-medium text-blue-800 dark:text-blue-200 mb-2\">\n          Tips for Best Results:\n        </h3>\n        <ul className=\"text-sm text-blue-700 dark:text-blue-300 space-y-1\">\n          <li>• Use videos with clear organ sound (minimal background noise)</li>\n          <li>• Longer recordings (5+ minutes) provide more accurate analysis</li>\n          <li>• Include metadata for better research insights</li>\n          <li>• Videos with single stops or simple registrations work best</li>\n        </ul>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAWe,SAAS,WAAW,EACjC,eAAe,EACf,kBAAkB,EAClB,eAAe,EACf,WAAW,EACK;IAChB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,WAAW,IAAI,IAAI;YACtB,MAAM;YACN;QACF;QAEA;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,iCAAiC;gBAC5D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,aAAa;oBACb,YAAY;oBACZ,WAAW,WAAW,SAAS,YAAY;oBAC3C,kBAAkB,kBAAkB,SAAS,mBAAmB;oBAChE,UAAU,YAAY;oBACtB,aAAa,cAAc;gBAC7B;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,mBAAmB;QACrB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;YACjC,MAAM;YACN;QACF;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAU;0BAAwD;;;;;;0BAItE,8OAAC;gBAAK,UAAU;gBAAc,WAAU;;kCAEtC,8OAAC;;0CACC,8OAAC;gCAAM,SAAQ;gCAAc,WAAU;0CAAkE;;;;;;0CAGzG,8OAAC;gCACC,MAAK;gCACL,IAAG;gCACH,OAAO;gCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gCAC7C,aAAY;gCACZ,WAAU;gCACV,QAAQ;gCACR,UAAU;;;;;;;;;;;;kCAKd,8OAAC;;0CACC,8OAAC;gCAAM,SAAQ;gCAAa,WAAU;0CAAkE;;;;;;0CAGxG,8OAAC;gCACC,IAAG;gCACH,OAAO;gCACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;gCAC5C,WAAU;gCACV,UAAU;;kDAEV,8OAAC;wCAAO,OAAM;kDAAa;;;;;;kDAC3B,8OAAC;wCAAO,OAAM;kDAAW;;;;;;kDACzB,8OAAC;wCAAO,OAAM;kDAAS;;;;;;;;;;;;;;;;;;kCAK3B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAM,SAAQ;wCAAY,WAAU;kDAAkE;;;;;;kDAGvG,8OAAC;wCACC,MAAK;wCACL,IAAG;wCACH,OAAO;wCACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;wCAC3C,aAAY;wCACZ,KAAI;wCACJ,KAAI;wCACJ,WAAU;wCACV,UAAU;;;;;;;;;;;;0CAId,8OAAC;;kDACC,8OAAC;wCAAM,SAAQ;wCAAmB,WAAU;kDAAkE;;;;;;kDAG9G,8OAAC;wCACC,MAAK;wCACL,IAAG;wCACH,OAAO;wCACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;wCAClD,aAAY;wCACZ,KAAI;wCACJ,KAAI;wCACJ,WAAU;wCACV,UAAU;;;;;;;;;;;;;;;;;;kCAKhB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAM,SAAQ;wCAAc,WAAU;kDAAkE;;;;;;kDAGzG,8OAAC;wCACC,MAAK;wCACL,IAAG;wCACH,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,aAAY;wCACZ,WAAU;wCACV,UAAU;;;;;;;;;;;;0CAId,8OAAC;;kDACC,8OAAC;wCAAM,SAAQ;wCAAW,WAAU;kDAAkE;;;;;;kDAGtG,8OAAC;wCACC,MAAK;wCACL,IAAG;wCACH,OAAO;wCACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;wCAC3C,aAAY;wCACZ,WAAU;wCACV,UAAU;;;;;;;;;;;;;;;;;;kCAMhB,8OAAC;wBACC,MAAK;wBACL,UAAU;wBACV,WAAU;kCAET,4BACC;;8CACE,8OAAC;oCAAI,WAAU;oCAA6C,OAAM;oCAA6B,MAAK;oCAAO,SAAQ;;sDACjH,8OAAC;4CAAO,WAAU;4CAAa,IAAG;4CAAK,IAAG;4CAAK,GAAE;4CAAK,QAAO;4CAAe,aAAY;;;;;;sDACxF,8OAAC;4CAAK,WAAU;4CAAa,MAAK;4CAAe,GAAE;;;;;;;;;;;;gCAC/C;;2CAIR;;;;;;;;;;;;0BAMN,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA4D;;;;;;kCAG1E,8OAAC;wBAAG,WAAU;;0CACZ,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAG;;;;;;;;;;;;;;;;;;;;;;;;AAKd", "debugId": null}}, {"offset": {"line": 425, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/youtube_spider_analyzer/organ-timbre-analyzer/src/components/ResultsDisplay.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\n\ninterface TimbreFeatures {\n  spectral_centroid: number;\n  spectral_rolloff: number;\n  spectral_bandwidth: number;\n  spectral_contrast: number[];\n  harmonic_ratios: number[];\n  inharmonicity_index: number;\n  fundamental_frequency: number;\n  mfcc_coefficients: number[];\n  attack_time: number;\n  decay_time: number;\n  sustain_level: number;\n  roughness: number;\n  brightness_index: number;\n  warmth_index: number;\n  pipe_speech_clarity: number;\n  wind_system_stability: number;\n  room_resonance_factor: number;\n}\n\ninterface VideoAnalysis {\n  id?: string;\n  youtube_url: string;\n  title: string;\n  organ_type: string;\n  organ_age?: number;\n  restoration_date?: number;\n  location?: string;\n  church_name?: string;\n  timbre_features: TimbreFeatures;\n  metadata: any;\n  analysis_date: string;\n  confidence_score?: number;\n}\n\ninterface ResultsDisplayProps {\n  analysis: VideoAnalysis;\n}\n\nexport default function ResultsDisplay({ analysis }: ResultsDisplayProps) {\n  const [activeTab, setActiveTab] = useState('overview');\n\n  const formatNumber = (num: number, decimals: number = 3) => {\n    return num.toFixed(decimals);\n  };\n\n  const getOrganTypeColor = (type: string) => {\n    switch (type) {\n      case 'historical': return 'bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-200';\n      case 'restored': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';\n      case 'modern': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';\n      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';\n    }\n  };\n\n  const getBrightnessLevel = (brightness: number) => {\n    if (brightness > 0.3) return { level: 'Bright', color: 'text-yellow-600' };\n    if (brightness > 0.15) return { level: 'Moderate', color: 'text-orange-600' };\n    return { level: 'Warm', color: 'text-red-600' };\n  };\n\n  const getStabilityLevel = (stability: number) => {\n    if (stability > 0.8) return { level: 'Very Stable', color: 'text-green-600' };\n    if (stability > 0.6) return { level: 'Stable', color: 'text-blue-600' };\n    if (stability > 0.4) return { level: 'Moderate', color: 'text-yellow-600' };\n    return { level: 'Unstable', color: 'text-red-600' };\n  };\n\n  const tabs = [\n    { id: 'overview', label: 'Overview' },\n    { id: 'spectral', label: 'Spectral Analysis' },\n    { id: 'harmonic', label: 'Harmonic Content' },\n    { id: 'temporal', label: 'Temporal Features' },\n    { id: 'organ', label: 'Organ Characteristics' }\n  ];\n\n  return (\n    <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden\">\n      {/* Header */}\n      <div className=\"bg-gradient-to-r from-blue-600 to-indigo-600 text-white p-6\">\n        <div className=\"flex items-start justify-between\">\n          <div>\n            <h2 className=\"text-2xl font-bold mb-2\">Analysis Results</h2>\n            <h3 className=\"text-lg opacity-90\">{analysis.title}</h3>\n            {analysis.church_name && (\n              <p className=\"text-sm opacity-80 mt-1\">{analysis.church_name}</p>\n            )}\n            {analysis.location && (\n              <p className=\"text-sm opacity-80\">{analysis.location}</p>\n            )}\n          </div>\n          <div className=\"text-right\">\n            <span className={`inline-block px-3 py-1 rounded-full text-sm font-medium ${getOrganTypeColor(analysis.organ_type)}`}>\n              {analysis.organ_type.charAt(0).toUpperCase() + analysis.organ_type.slice(1)}\n            </span>\n            {analysis.organ_age && (\n              <p className=\"text-sm opacity-80 mt-2\">Built: {analysis.organ_age}</p>\n            )}\n            {analysis.restoration_date && (\n              <p className=\"text-sm opacity-80\">Restored: {analysis.restoration_date}</p>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* Tabs */}\n      <div className=\"border-b border-gray-200 dark:border-gray-700\">\n        <nav className=\"flex space-x-8 px-6\">\n          {tabs.map((tab) => (\n            <button\n              key={tab.id}\n              onClick={() => setActiveTab(tab.id)}\n              className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${\n                activeTab === tab.id\n                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'\n                  : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'\n              }`}\n            >\n              {tab.label}\n            </button>\n          ))}\n        </nav>\n      </div>\n\n      {/* Tab Content */}\n      <div className=\"p-6\">\n        {activeTab === 'overview' && (\n          <div className=\"space-y-6\">\n            {/* Key Metrics */}\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n              <div className=\"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg\">\n                <h4 className=\"font-medium text-gray-700 dark:text-gray-300 mb-2\">Brightness</h4>\n                <div className=\"flex items-center justify-between\">\n                  <span className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                    {formatNumber(analysis.timbre_features.brightness_index * 100, 1)}%\n                  </span>\n                  <span className={`text-sm font-medium ${getBrightnessLevel(analysis.timbre_features.brightness_index).color}`}>\n                    {getBrightnessLevel(analysis.timbre_features.brightness_index).level}\n                  </span>\n                </div>\n              </div>\n\n              <div className=\"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg\">\n                <h4 className=\"font-medium text-gray-700 dark:text-gray-300 mb-2\">Warmth</h4>\n                <div className=\"flex items-center justify-between\">\n                  <span className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                    {formatNumber(analysis.timbre_features.warmth_index * 100, 1)}%\n                  </span>\n                  <span className=\"text-sm font-medium text-orange-600\">\n                    Low Freq Energy\n                  </span>\n                </div>\n              </div>\n\n              <div className=\"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg\">\n                <h4 className=\"font-medium text-gray-700 dark:text-gray-300 mb-2\">Wind Stability</h4>\n                <div className=\"flex items-center justify-between\">\n                  <span className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                    {formatNumber(analysis.timbre_features.wind_system_stability * 100, 1)}%\n                  </span>\n                  <span className={`text-sm font-medium ${getStabilityLevel(analysis.timbre_features.wind_system_stability).color}`}>\n                    {getStabilityLevel(analysis.timbre_features.wind_system_stability).level}\n                  </span>\n                </div>\n              </div>\n            </div>\n\n            {/* Summary */}\n            <div className=\"bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg\">\n              <h4 className=\"font-medium text-blue-800 dark:text-blue-200 mb-2\">Analysis Summary</h4>\n              <p className=\"text-blue-700 dark:text-blue-300 text-sm\">\n                This {analysis.organ_type} organ shows {getBrightnessLevel(analysis.timbre_features.brightness_index).level.toLowerCase()} \n                timbral characteristics with {getStabilityLevel(analysis.timbre_features.wind_system_stability).level.toLowerCase()} \n                wind system performance. The fundamental frequency is {formatNumber(analysis.timbre_features.fundamental_frequency, 1)} Hz \n                with an inharmonicity index of {formatNumber(analysis.timbre_features.inharmonicity_index, 3)}.\n              </p>\n            </div>\n          </div>\n        )}\n\n        {activeTab === 'spectral' && (\n          <div className=\"space-y-4\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <h4 className=\"font-medium text-gray-700 dark:text-gray-300 mb-2\">Spectral Centroid</h4>\n                <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                  {formatNumber(analysis.timbre_features.spectral_centroid, 1)} Hz\n                </p>\n                <p className=\"text-sm text-gray-600 dark:text-gray-400\">Center of spectral mass</p>\n              </div>\n              \n              <div>\n                <h4 className=\"font-medium text-gray-700 dark:text-gray-300 mb-2\">Spectral Rolloff</h4>\n                <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                  {formatNumber(analysis.timbre_features.spectral_rolloff, 1)} Hz\n                </p>\n                <p className=\"text-sm text-gray-600 dark:text-gray-400\">85% energy cutoff frequency</p>\n              </div>\n\n              <div>\n                <h4 className=\"font-medium text-gray-700 dark:text-gray-300 mb-2\">Spectral Bandwidth</h4>\n                <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                  {formatNumber(analysis.timbre_features.spectral_bandwidth, 1)} Hz\n                </p>\n                <p className=\"text-sm text-gray-600 dark:text-gray-400\">Frequency spread</p>\n              </div>\n\n              <div>\n                <h4 className=\"font-medium text-gray-700 dark:text-gray-300 mb-2\">Roughness</h4>\n                <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                  {formatNumber(analysis.timbre_features.roughness, 3)}\n                </p>\n                <p className=\"text-sm text-gray-600 dark:text-gray-400\">Perceptual roughness</p>\n              </div>\n            </div>\n\n            <div>\n              <h4 className=\"font-medium text-gray-700 dark:text-gray-300 mb-2\">Spectral Contrast</h4>\n              <div className=\"grid grid-cols-7 gap-2\">\n                {analysis.timbre_features.spectral_contrast.map((contrast, index) => (\n                  <div key={index} className=\"text-center\">\n                    <div className=\"bg-gray-200 dark:bg-gray-600 h-16 rounded flex items-end\">\n                      <div \n                        className=\"bg-blue-500 w-full rounded-b\"\n                        style={{ height: `${Math.max(contrast * 100, 5)}%` }}\n                      ></div>\n                    </div>\n                    <p className=\"text-xs text-gray-600 dark:text-gray-400 mt-1\">Band {index + 1}</p>\n                  </div>\n                ))}\n              </div>\n            </div>\n          </div>\n        )}\n\n        {activeTab === 'harmonic' && (\n          <div className=\"space-y-4\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <h4 className=\"font-medium text-gray-700 dark:text-gray-300 mb-2\">Fundamental Frequency</h4>\n                <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                  {formatNumber(analysis.timbre_features.fundamental_frequency, 1)} Hz\n                </p>\n              </div>\n              \n              <div>\n                <h4 className=\"font-medium text-gray-700 dark:text-gray-300 mb-2\">Inharmonicity Index</h4>\n                <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                  {formatNumber(analysis.timbre_features.inharmonicity_index, 4)}\n                </p>\n              </div>\n            </div>\n\n            <div>\n              <h4 className=\"font-medium text-gray-700 dark:text-gray-300 mb-2\">Harmonic Ratios</h4>\n              <div className=\"grid grid-cols-7 gap-2\">\n                {analysis.timbre_features.harmonic_ratios.map((ratio, index) => (\n                  <div key={index} className=\"text-center\">\n                    <div className=\"bg-gray-200 dark:bg-gray-600 h-20 rounded flex items-end\">\n                      <div \n                        className=\"bg-green-500 w-full rounded-b\"\n                        style={{ height: `${Math.max(ratio * 100, 5)}%` }}\n                      ></div>\n                    </div>\n                    <p className=\"text-xs text-gray-600 dark:text-gray-400 mt-1\">{index + 2}nd</p>\n                    <p className=\"text-xs font-medium text-gray-900 dark:text-white\">{formatNumber(ratio, 2)}</p>\n                  </div>\n                ))}\n              </div>\n            </div>\n          </div>\n        )}\n\n        {activeTab === 'temporal' && (\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n            <div>\n              <h4 className=\"font-medium text-gray-700 dark:text-gray-300 mb-2\">Attack Time</h4>\n              <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                {formatNumber(analysis.timbre_features.attack_time * 1000, 1)} ms\n              </p>\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">Time to reach peak</p>\n            </div>\n            \n            <div>\n              <h4 className=\"font-medium text-gray-700 dark:text-gray-300 mb-2\">Decay Time</h4>\n              <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                {formatNumber(analysis.timbre_features.decay_time, 2)} s\n              </p>\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">Time to 50% amplitude</p>\n            </div>\n\n            <div>\n              <h4 className=\"font-medium text-gray-700 dark:text-gray-300 mb-2\">Sustain Level</h4>\n              <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                {formatNumber(analysis.timbre_features.sustain_level, 3)}\n              </p>\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">Sustained amplitude</p>\n            </div>\n          </div>\n        )}\n\n        {activeTab === 'organ' && (\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div>\n              <h4 className=\"font-medium text-gray-700 dark:text-gray-300 mb-2\">Pipe Speech Clarity</h4>\n              <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                {formatNumber(analysis.timbre_features.pipe_speech_clarity, 3)}\n              </p>\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">Attack transient sharpness</p>\n            </div>\n            \n            <div>\n              <h4 className=\"font-medium text-gray-700 dark:text-gray-300 mb-2\">Wind System Stability</h4>\n              <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                {formatNumber(analysis.timbre_features.wind_system_stability * 100, 1)}%\n              </p>\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">Amplitude consistency</p>\n            </div>\n\n            <div>\n              <h4 className=\"font-medium text-gray-700 dark:text-gray-300 mb-2\">Room Resonance Factor</h4>\n              <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                {formatNumber(analysis.timbre_features.room_resonance_factor, 3)}\n              </p>\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">Acoustic environment influence</p>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AA2Ce,SAAS,eAAe,EAAE,QAAQ,EAAuB;IACtE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,eAAe,CAAC,KAAa,WAAmB,CAAC;QACrD,OAAO,IAAI,OAAO,CAAC;IACrB;IAEA,MAAM,oBAAoB,CAAC;QACzB,OAAQ;YACN,KAAK;gBAAc,OAAO;YAC1B,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,IAAI,aAAa,KAAK,OAAO;YAAE,OAAO;YAAU,OAAO;QAAkB;QACzE,IAAI,aAAa,MAAM,OAAO;YAAE,OAAO;YAAY,OAAO;QAAkB;QAC5E,OAAO;YAAE,OAAO;YAAQ,OAAO;QAAe;IAChD;IAEA,MAAM,oBAAoB,CAAC;QACzB,IAAI,YAAY,KAAK,OAAO;YAAE,OAAO;YAAe,OAAO;QAAiB;QAC5E,IAAI,YAAY,KAAK,OAAO;YAAE,OAAO;YAAU,OAAO;QAAgB;QACtE,IAAI,YAAY,KAAK,OAAO;YAAE,OAAO;YAAY,OAAO;QAAkB;QAC1E,OAAO;YAAE,OAAO;YAAY,OAAO;QAAe;IACpD;IAEA,MAAM,OAAO;QACX;YAAE,IAAI;YAAY,OAAO;QAAW;QACpC;YAAE,IAAI;YAAY,OAAO;QAAoB;QAC7C;YAAE,IAAI;YAAY,OAAO;QAAmB;QAC5C;YAAE,IAAI;YAAY,OAAO;QAAoB;QAC7C;YAAE,IAAI;YAAS,OAAO;QAAwB;KAC/C;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA0B;;;;;;8CACxC,8OAAC;oCAAG,WAAU;8CAAsB,SAAS,KAAK;;;;;;gCACjD,SAAS,WAAW,kBACnB,8OAAC;oCAAE,WAAU;8CAA2B,SAAS,WAAW;;;;;;gCAE7D,SAAS,QAAQ,kBAChB,8OAAC;oCAAE,WAAU;8CAAsB,SAAS,QAAQ;;;;;;;;;;;;sCAGxD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAW,CAAC,wDAAwD,EAAE,kBAAkB,SAAS,UAAU,GAAG;8CACjH,SAAS,UAAU,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,SAAS,UAAU,CAAC,KAAK,CAAC;;;;;;gCAE1E,SAAS,SAAS,kBACjB,8OAAC;oCAAE,WAAU;;wCAA0B;wCAAQ,SAAS,SAAS;;;;;;;gCAElE,SAAS,gBAAgB,kBACxB,8OAAC;oCAAE,WAAU;;wCAAqB;wCAAW,SAAS,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;0BAO9E,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACZ,KAAK,GAAG,CAAC,CAAC,oBACT,8OAAC;4BAEC,SAAS,IAAM,aAAa,IAAI,EAAE;4BAClC,WAAW,CAAC,2DAA2D,EACrE,cAAc,IAAI,EAAE,GAChB,qDACA,oGACJ;sCAED,IAAI,KAAK;2BARL,IAAI,EAAE;;;;;;;;;;;;;;;0BAenB,8OAAC;gBAAI,WAAU;;oBACZ,cAAc,4BACb,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAoD;;;;;;0DAClE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;;4DACb,aAAa,SAAS,eAAe,CAAC,gBAAgB,GAAG,KAAK;4DAAG;;;;;;;kEAEpE,8OAAC;wDAAK,WAAW,CAAC,oBAAoB,EAAE,mBAAmB,SAAS,eAAe,CAAC,gBAAgB,EAAE,KAAK,EAAE;kEAC1G,mBAAmB,SAAS,eAAe,CAAC,gBAAgB,EAAE,KAAK;;;;;;;;;;;;;;;;;;kDAK1E,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAoD;;;;;;0DAClE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;;4DACb,aAAa,SAAS,eAAe,CAAC,YAAY,GAAG,KAAK;4DAAG;;;;;;;kEAEhE,8OAAC;wDAAK,WAAU;kEAAsC;;;;;;;;;;;;;;;;;;kDAM1D,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAoD;;;;;;0DAClE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;;4DACb,aAAa,SAAS,eAAe,CAAC,qBAAqB,GAAG,KAAK;4DAAG;;;;;;;kEAEzE,8OAAC;wDAAK,WAAW,CAAC,oBAAoB,EAAE,kBAAkB,SAAS,eAAe,CAAC,qBAAqB,EAAE,KAAK,EAAE;kEAC9G,kBAAkB,SAAS,eAAe,CAAC,qBAAqB,EAAE,KAAK;;;;;;;;;;;;;;;;;;;;;;;;0CAOhF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAoD;;;;;;kDAClE,8OAAC;wCAAE,WAAU;;4CAA2C;4CAChD,SAAS,UAAU;4CAAC;4CAAc,mBAAmB,SAAS,eAAe,CAAC,gBAAgB,EAAE,KAAK,CAAC,WAAW;4CAAG;4CAC5F,kBAAkB,SAAS,eAAe,CAAC,qBAAqB,EAAE,KAAK,CAAC,WAAW;4CAAG;4CAC7D,aAAa,SAAS,eAAe,CAAC,qBAAqB,EAAE;4CAAG;4CACvF,aAAa,SAAS,eAAe,CAAC,mBAAmB,EAAE;4CAAG;;;;;;;;;;;;;;;;;;;oBAMrG,cAAc,4BACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAoD;;;;;;0DAClE,8OAAC;gDAAE,WAAU;;oDACV,aAAa,SAAS,eAAe,CAAC,iBAAiB,EAAE;oDAAG;;;;;;;0DAE/D,8OAAC;gDAAE,WAAU;0DAA2C;;;;;;;;;;;;kDAG1D,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAoD;;;;;;0DAClE,8OAAC;gDAAE,WAAU;;oDACV,aAAa,SAAS,eAAe,CAAC,gBAAgB,EAAE;oDAAG;;;;;;;0DAE9D,8OAAC;gDAAE,WAAU;0DAA2C;;;;;;;;;;;;kDAG1D,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAoD;;;;;;0DAClE,8OAAC;gDAAE,WAAU;;oDACV,aAAa,SAAS,eAAe,CAAC,kBAAkB,EAAE;oDAAG;;;;;;;0DAEhE,8OAAC;gDAAE,WAAU;0DAA2C;;;;;;;;;;;;kDAG1D,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAoD;;;;;;0DAClE,8OAAC;gDAAE,WAAU;0DACV,aAAa,SAAS,eAAe,CAAC,SAAS,EAAE;;;;;;0DAEpD,8OAAC;gDAAE,WAAU;0DAA2C;;;;;;;;;;;;;;;;;;0CAI5D,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAoD;;;;;;kDAClE,8OAAC;wCAAI,WAAU;kDACZ,SAAS,eAAe,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,UAAU,sBACzD,8OAAC;gDAAgB,WAAU;;kEACzB,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DACC,WAAU;4DACV,OAAO;gEAAE,QAAQ,GAAG,KAAK,GAAG,CAAC,WAAW,KAAK,GAAG,CAAC,CAAC;4DAAC;;;;;;;;;;;kEAGvD,8OAAC;wDAAE,WAAU;;4DAAgD;4DAAM,QAAQ;;;;;;;;+CAPnE;;;;;;;;;;;;;;;;;;;;;;oBAenB,cAAc,4BACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAoD;;;;;;0DAClE,8OAAC;gDAAE,WAAU;;oDACV,aAAa,SAAS,eAAe,CAAC,qBAAqB,EAAE;oDAAG;;;;;;;;;;;;;kDAIrE,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAoD;;;;;;0DAClE,8OAAC;gDAAE,WAAU;0DACV,aAAa,SAAS,eAAe,CAAC,mBAAmB,EAAE;;;;;;;;;;;;;;;;;;0CAKlE,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAoD;;;;;;kDAClE,8OAAC;wCAAI,WAAU;kDACZ,SAAS,eAAe,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,OAAO,sBACpD,8OAAC;gDAAgB,WAAU;;kEACzB,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DACC,WAAU;4DACV,OAAO;gEAAE,QAAQ,GAAG,KAAK,GAAG,CAAC,QAAQ,KAAK,GAAG,CAAC,CAAC;4DAAC;;;;;;;;;;;kEAGpD,8OAAC;wDAAE,WAAU;;4DAAiD,QAAQ;4DAAE;;;;;;;kEACxE,8OAAC;wDAAE,WAAU;kEAAqD,aAAa,OAAO;;;;;;;+CAR9E;;;;;;;;;;;;;;;;;;;;;;oBAgBnB,cAAc,4BACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAoD;;;;;;kDAClE,8OAAC;wCAAE,WAAU;;4CACV,aAAa,SAAS,eAAe,CAAC,WAAW,GAAG,MAAM;4CAAG;;;;;;;kDAEhE,8OAAC;wCAAE,WAAU;kDAA2C;;;;;;;;;;;;0CAG1D,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAoD;;;;;;kDAClE,8OAAC;wCAAE,WAAU;;4CACV,aAAa,SAAS,eAAe,CAAC,UAAU,EAAE;4CAAG;;;;;;;kDAExD,8OAAC;wCAAE,WAAU;kDAA2C;;;;;;;;;;;;0CAG1D,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAoD;;;;;;kDAClE,8OAAC;wCAAE,WAAU;kDACV,aAAa,SAAS,eAAe,CAAC,aAAa,EAAE;;;;;;kDAExD,8OAAC;wCAAE,WAAU;kDAA2C;;;;;;;;;;;;;;;;;;oBAK7D,cAAc,yBACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAoD;;;;;;kDAClE,8OAAC;wCAAE,WAAU;kDACV,aAAa,SAAS,eAAe,CAAC,mBAAmB,EAAE;;;;;;kDAE9D,8OAAC;wCAAE,WAAU;kDAA2C;;;;;;;;;;;;0CAG1D,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAoD;;;;;;kDAClE,8OAAC;wCAAE,WAAU;;4CACV,aAAa,SAAS,eAAe,CAAC,qBAAqB,GAAG,KAAK;4CAAG;;;;;;;kDAEzE,8OAAC;wCAAE,WAAU;kDAA2C;;;;;;;;;;;;0CAG1D,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAoD;;;;;;kDAClE,8OAAC;wCAAE,WAAU;kDACV,aAAa,SAAS,eAAe,CAAC,qBAAqB,EAAE;;;;;;kDAEhE,8OAAC;wCAAE,WAAU;kDAA2C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOtE", "debugId": null}}, {"offset": {"line": 1408, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/youtube_spider_analyzer/organ-timbre-analyzer/src/components/StatisticsPanel.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\n\ninterface StatisticalSummary {\n  total_analyses: number;\n  historical_count: number;\n  modern_count: number;\n  restored_count: number;\n  avg_brightness_historical: number;\n  avg_brightness_modern: number;\n  avg_brightness_restored: number;\n  avg_warmth_historical: number;\n  avg_warmth_modern: number;\n  avg_warmth_restored: number;\n  significant_differences: any[];\n}\n\nexport default function StatisticsPanel() {\n  const [stats, setStats] = useState<StatisticalSummary | null>(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    fetchStatistics();\n  }, []);\n\n  const fetchStatistics = async () => {\n    try {\n      const response = await fetch('http://localhost:8000/statistics');\n      if (response.ok) {\n        const data = await response.json();\n        setStats(data);\n      }\n    } catch (error) {\n      console.error('Failed to fetch statistics:', error);\n      // Set placeholder data for demo\n      setStats({\n        total_analyses: 0,\n        historical_count: 0,\n        modern_count: 0,\n        restored_count: 0,\n        avg_brightness_historical: 0,\n        avg_brightness_modern: 0,\n        avg_brightness_restored: 0,\n        avg_warmth_historical: 0,\n        avg_warmth_modern: 0,\n        avg_warmth_restored: 0,\n        significant_differences: []\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\">\n        <div className=\"animate-pulse\">\n          <div className=\"h-6 bg-gray-200 dark:bg-gray-700 rounded mb-4\"></div>\n          <div className=\"space-y-3\">\n            <div className=\"h-4 bg-gray-200 dark:bg-gray-700 rounded\"></div>\n            <div className=\"h-4 bg-gray-200 dark:bg-gray-700 rounded\"></div>\n            <div className=\"h-4 bg-gray-200 dark:bg-gray-700 rounded\"></div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Database Statistics */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\">\n        <h3 className=\"text-xl font-bold text-gray-800 dark:text-white mb-4\">\n          Database Statistics\n        </h3>\n        \n        <div className=\"space-y-4\">\n          <div className=\"flex justify-between items-center\">\n            <span className=\"text-gray-600 dark:text-gray-400\">Total Analyses</span>\n            <span className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n              {stats?.total_analyses || 0}\n            </span>\n          </div>\n          \n          <div className=\"space-y-2\">\n            <div className=\"flex justify-between items-center\">\n              <span className=\"text-sm text-gray-600 dark:text-gray-400\">Historical Organs</span>\n              <span className=\"font-medium text-amber-600\">\n                {stats?.historical_count || 0}\n              </span>\n            </div>\n            \n            <div className=\"flex justify-between items-center\">\n              <span className=\"text-sm text-gray-600 dark:text-gray-400\">Restored Organs</span>\n              <span className=\"font-medium text-blue-600\">\n                {stats?.restored_count || 0}\n              </span>\n            </div>\n            \n            <div className=\"flex justify-between items-center\">\n              <span className=\"text-sm text-gray-600 dark:text-gray-400\">Modern Organs</span>\n              <span className=\"font-medium text-green-600\">\n                {stats?.modern_count || 0}\n              </span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Research Insights */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\">\n        <h3 className=\"text-xl font-bold text-gray-800 dark:text-white mb-4\">\n          Research Insights\n        </h3>\n        \n        {stats && stats.total_analyses > 0 ? (\n          <div className=\"space-y-4\">\n            <div>\n              <h4 className=\"font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                Average Brightness by Type\n              </h4>\n              <div className=\"space-y-2\">\n                <div className=\"flex justify-between items-center\">\n                  <span className=\"text-sm text-amber-600\">Historical</span>\n                  <span className=\"font-medium\">\n                    {(stats.avg_brightness_historical * 100).toFixed(1)}%\n                  </span>\n                </div>\n                <div className=\"flex justify-between items-center\">\n                  <span className=\"text-sm text-blue-600\">Restored</span>\n                  <span className=\"font-medium\">\n                    {(stats.avg_brightness_restored * 100).toFixed(1)}%\n                  </span>\n                </div>\n                <div className=\"flex justify-between items-center\">\n                  <span className=\"text-sm text-green-600\">Modern</span>\n                  <span className=\"font-medium\">\n                    {(stats.avg_brightness_modern * 100).toFixed(1)}%\n                  </span>\n                </div>\n              </div>\n            </div>\n\n            <div>\n              <h4 className=\"font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                Average Warmth by Type\n              </h4>\n              <div className=\"space-y-2\">\n                <div className=\"flex justify-between items-center\">\n                  <span className=\"text-sm text-amber-600\">Historical</span>\n                  <span className=\"font-medium\">\n                    {(stats.avg_warmth_historical * 100).toFixed(1)}%\n                  </span>\n                </div>\n                <div className=\"flex justify-between items-center\">\n                  <span className=\"text-sm text-blue-600\">Restored</span>\n                  <span className=\"font-medium\">\n                    {(stats.avg_warmth_restored * 100).toFixed(1)}%\n                  </span>\n                </div>\n                <div className=\"flex justify-between items-center\">\n                  <span className=\"text-sm text-green-600\">Modern</span>\n                  <span className=\"font-medium\">\n                    {(stats.avg_warmth_modern * 100).toFixed(1)}%\n                  </span>\n                </div>\n              </div>\n            </div>\n          </div>\n        ) : (\n          <div className=\"text-center py-8\">\n            <div className=\"text-gray-400 dark:text-gray-500 mb-2\">\n              <svg className=\"mx-auto h-12 w-12\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n              </svg>\n            </div>\n            <p className=\"text-gray-500 dark:text-gray-400 text-sm\">\n              No analyses yet. Start analyzing organs to see research insights!\n            </p>\n          </div>\n        )}\n      </div>\n\n      {/* Quick Actions */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\">\n        <h3 className=\"text-xl font-bold text-gray-800 dark:text-white mb-4\">\n          Quick Actions\n        </h3>\n        \n        <div className=\"space-y-3\">\n          <button className=\"w-full text-left p-3 rounded-lg border border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\">\n            <div className=\"font-medium text-gray-900 dark:text-white\">Compare Analyses</div>\n            <div className=\"text-sm text-gray-600 dark:text-gray-400\">\n              Compare two organ recordings\n            </div>\n          </button>\n          \n          <button className=\"w-full text-left p-3 rounded-lg border border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\">\n            <div className=\"font-medium text-gray-900 dark:text-white\">Export Data</div>\n            <div className=\"text-sm text-gray-600 dark:text-gray-400\">\n              Download analysis results\n            </div>\n          </button>\n          \n          <button className=\"w-full text-left p-3 rounded-lg border border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\">\n            <div className=\"font-medium text-gray-900 dark:text-white\">View All Analyses</div>\n            <div className=\"text-sm text-gray-600 dark:text-gray-400\">\n              Browse analysis history\n            </div>\n          </button>\n        </div>\n      </div>\n\n      {/* Research Tips */}\n      <div className=\"bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-lg p-6\">\n        <h3 className=\"text-lg font-bold text-purple-800 dark:text-purple-200 mb-3\">\n          Research Tips\n        </h3>\n        \n        <div className=\"space-y-2 text-sm text-purple-700 dark:text-purple-300\">\n          <p>• Analyze multiple recordings from the same organ for consistency</p>\n          <p>• Compare organs from similar time periods and regions</p>\n          <p>• Look for patterns in restoration vs. original recordings</p>\n          <p>• Consider room acoustics when interpreting results</p>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAkBe,SAAS;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA6B;IAC9D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,kBAAkB;QACtB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,SAAS;YACX;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,gCAAgC;YAChC,SAAS;gBACP,gBAAgB;gBAChB,kBAAkB;gBAClB,cAAc;gBACd,gBAAgB;gBAChB,2BAA2B;gBAC3B,uBAAuB;gBACvB,yBAAyB;gBACzB,uBAAuB;gBACvB,mBAAmB;gBACnB,qBAAqB;gBACrB,yBAAyB,EAAE;YAC7B;QACF,SAAU;YACR,WAAW;QACb;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;IAKzB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAuD;;;;;;kCAIrE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAmC;;;;;;kDACnD,8OAAC;wCAAK,WAAU;kDACb,OAAO,kBAAkB;;;;;;;;;;;;0CAI9B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAA2C;;;;;;0DAC3D,8OAAC;gDAAK,WAAU;0DACb,OAAO,oBAAoB;;;;;;;;;;;;kDAIhC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAA2C;;;;;;0DAC3D,8OAAC;gDAAK,WAAU;0DACb,OAAO,kBAAkB;;;;;;;;;;;;kDAI9B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAA2C;;;;;;0DAC3D,8OAAC;gDAAK,WAAU;0DACb,OAAO,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQlC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAuD;;;;;;oBAIpE,SAAS,MAAM,cAAc,GAAG,kBAC/B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAoD;;;;;;kDAGlE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAyB;;;;;;kEACzC,8OAAC;wDAAK,WAAU;;4DACb,CAAC,MAAM,yBAAyB,GAAG,GAAG,EAAE,OAAO,CAAC;4DAAG;;;;;;;;;;;;;0DAGxD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAwB;;;;;;kEACxC,8OAAC;wDAAK,WAAU;;4DACb,CAAC,MAAM,uBAAuB,GAAG,GAAG,EAAE,OAAO,CAAC;4DAAG;;;;;;;;;;;;;0DAGtD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAyB;;;;;;kEACzC,8OAAC;wDAAK,WAAU;;4DACb,CAAC,MAAM,qBAAqB,GAAG,GAAG,EAAE,OAAO,CAAC;4DAAG;;;;;;;;;;;;;;;;;;;;;;;;;0CAMxD,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAoD;;;;;;kDAGlE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAyB;;;;;;kEACzC,8OAAC;wDAAK,WAAU;;4DACb,CAAC,MAAM,qBAAqB,GAAG,GAAG,EAAE,OAAO,CAAC;4DAAG;;;;;;;;;;;;;0DAGpD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAwB;;;;;;kEACxC,8OAAC;wDAAK,WAAU;;4DACb,CAAC,MAAM,mBAAmB,GAAG,GAAG,EAAE,OAAO,CAAC;4DAAG;;;;;;;;;;;;;0DAGlD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAyB;;;;;;kEACzC,8OAAC;wDAAK,WAAU;;4DACb,CAAC,MAAM,iBAAiB,GAAG,GAAG,EAAE,OAAO,CAAC;4DAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;6CAOtD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;oCAAoB,MAAK;oCAAO,SAAQ;oCAAY,QAAO;8CACxE,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;0CAGzE,8OAAC;gCAAE,WAAU;0CAA2C;;;;;;;;;;;;;;;;;;0BAQ9D,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAuD;;;;;;kCAIrE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAO,WAAU;;kDAChB,8OAAC;wCAAI,WAAU;kDAA4C;;;;;;kDAC3D,8OAAC;wCAAI,WAAU;kDAA2C;;;;;;;;;;;;0CAK5D,8OAAC;gCAAO,WAAU;;kDAChB,8OAAC;wCAAI,WAAU;kDAA4C;;;;;;kDAC3D,8OAAC;wCAAI,WAAU;kDAA2C;;;;;;;;;;;;0CAK5D,8OAAC;gCAAO,WAAU;;kDAChB,8OAAC;wCAAI,WAAU;kDAA4C;;;;;;kDAC3D,8OAAC;wCAAI,WAAU;kDAA2C;;;;;;;;;;;;;;;;;;;;;;;;0BAQhE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA8D;;;;;;kCAI5E,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;0CAAE;;;;;;0CACH,8OAAC;0CAAE;;;;;;0CACH,8OAAC;0CAAE;;;;;;0CACH,8OAAC;0CAAE;;;;;;;;;;;;;;;;;;;;;;;;AAKb", "debugId": null}}, {"offset": {"line": 2097, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/youtube_spider_analyzer/organ-timbre-analyzer/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport SearchForm from '@/components/SearchForm';\nimport ResultsDisplay from '@/components/ResultsDisplay';\nimport StatisticsPanel from '@/components/StatisticsPanel';\n\nexport default function Home() {\n  const [analysisResult, setAnalysisResult] = useState(null);\n  const [isAnalyzing, setIsAnalyzing] = useState(false);\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800\">\n      <div className=\"container mx-auto px-4 py-8\">\n        {/* Header */}\n        <header className=\"text-center mb-12\">\n          <h1 className=\"text-4xl md:text-6xl font-bold text-gray-800 dark:text-white mb-4\">\n            Church Organ Timbre Analyzer\n          </h1>\n          <p className=\"text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto\">\n            Analyze and compare the timbral characteristics of historical vs. modern church organs\n            from YouTube recordings. Discover how restoration and time affect organ sound.\n          </p>\n        </header>\n\n        {/* Main Content */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n          {/* Search Form */}\n          <div className=\"lg:col-span-2\">\n            <SearchForm\n              onAnalysisStart={() => setIsAnalyzing(true)}\n              onAnalysisComplete={(result) => {\n                setAnalysisResult(result);\n                setIsAnalyzing(false);\n              }}\n              onAnalysisError={() => setIsAnalyzing(false)}\n              isAnalyzing={isAnalyzing}\n            />\n\n            {/* Results */}\n            {analysisResult && (\n              <div className=\"mt-8\">\n                <ResultsDisplay analysis={analysisResult} />\n              </div>\n            )}\n          </div>\n\n          {/* Statistics Panel */}\n          <div className=\"lg:col-span-1\">\n            <StatisticsPanel />\n          </div>\n        </div>\n\n        {/* Research Information */}\n        <section className=\"mt-16 bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8\">\n          <h2 className=\"text-2xl font-bold text-gray-800 dark:text-white mb-6\">\n            Research Focus: Timbral Changes in Church Organs\n          </h2>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            <div>\n              <h3 className=\"text-lg font-semibold text-gray-700 dark:text-gray-300 mb-3\">\n                What We Analyze\n              </h3>\n              <ul className=\"space-y-2 text-gray-600 dark:text-gray-400\">\n                <li>• Spectral characteristics (brightness, warmth)</li>\n                <li>• Harmonic content and ratios</li>\n                <li>• Attack and decay characteristics</li>\n                <li>• Wind system stability</li>\n                <li>• Room acoustics influence</li>\n                <li>• Inharmonicity and tuning</li>\n              </ul>\n            </div>\n            <div>\n              <h3 className=\"text-lg font-semibold text-gray-700 dark:text-gray-300 mb-3\">\n                Research Questions\n              </h3>\n              <ul className=\"space-y-2 text-gray-600 dark:text-gray-400\">\n                <li>• Do restored organs lose high-frequency content?</li>\n                <li>• How do modern wind systems affect timbre?</li>\n                <li>• What changes occur in pipe metal over time?</li>\n                <li>• How do restoration techniques alter sound?</li>\n              </ul>\n            </div>\n          </div>\n        </section>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAO,WAAU;;sCAChB,8OAAC;4BAAG,WAAU;sCAAoE;;;;;;sCAGlF,8OAAC;4BAAE,WAAU;sCAA6D;;;;;;;;;;;;8BAO5E,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,gIAAA,CAAA,UAAU;oCACT,iBAAiB,IAAM,eAAe;oCACtC,oBAAoB,CAAC;wCACnB,kBAAkB;wCAClB,eAAe;oCACjB;oCACA,iBAAiB,IAAM,eAAe;oCACtC,aAAa;;;;;;gCAId,gCACC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,oIAAA,CAAA,UAAc;wCAAC,UAAU;;;;;;;;;;;;;;;;;sCAMhC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,qIAAA,CAAA,UAAe;;;;;;;;;;;;;;;;8BAKpB,8OAAC;oBAAQ,WAAU;;sCACjB,8OAAC;4BAAG,WAAU;sCAAwD;;;;;;sCAGtE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA8D;;;;;;sDAG5E,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;;;;;;;;;;;;;8CAGR,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA8D;;;;;;sDAG5E,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQpB", "debugId": null}}]}