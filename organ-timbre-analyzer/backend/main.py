from fastapi import <PERSON><PERSON><PERSON>, HTTPException, BackgroundTasks, UploadFile, File, Form
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, HttpUrl
from typing import Optional, Dict, List, Any
import os
import json
from dotenv import load_dotenv

from services.youtube_service import YouTubeService
from services.timbre_analyzer import TimbreAnalyzer
from services.comparative_analyzer import ComparativeAnalyzer
from services.autonomous_discovery import AutonomousDiscoveryService
from services.user_data_service import UserDataService
from models.analysis_models import VideoAnalysis, TimbreFeatures, ComparisonResult

load_dotenv()

app = FastAPI(title="Church Organ Timbre Analyzer", version="1.0.0")

# CORS middleware for Next.js frontend
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize services
youtube_service = YouTubeService()
timbre_analyzer = TimbreAnalyzer()
comparative_analyzer = ComparativeAnalyzer()
autonomous_discovery = AutonomousDiscoveryService()
user_data_service = UserDataService()

class VideoAnalysisRequest(BaseModel):
    youtube_url: HttpUrl
    organ_type: str  # "historical", "modern", "restored"
    organ_age: Optional[int] = None
    restoration_date: Optional[int] = None
    location: Optional[str] = None
    church_name: Optional[str] = None

class ComparisonRequest(BaseModel):
    video_id_1: str
    video_id_2: str

@app.get("/")
async def root():
    return {"message": "Church Organ Timbre Analyzer API"}

@app.post("/analyze", response_model=VideoAnalysis)
async def analyze_video(request: VideoAnalysisRequest, background_tasks: BackgroundTasks):
    """Analyze a YouTube video for organ timbre characteristics"""
    try:
        # Download and extract audio
        audio_path = await youtube_service.download_audio(str(request.youtube_url))
        
        # Extract video metadata
        metadata = await youtube_service.get_video_metadata(str(request.youtube_url))
        
        # Perform timbre analysis
        timbre_features = await timbre_analyzer.analyze_audio(audio_path)
        
        # Create analysis result
        analysis = VideoAnalysis(
            youtube_url=str(request.youtube_url),
            title=metadata.get("title", ""),
            organ_type=request.organ_type,
            organ_age=request.organ_age,
            restoration_date=request.restoration_date,
            location=request.location,
            church_name=request.church_name,
            timbre_features=timbre_features,
            metadata=metadata
        )
        
        # Clean up audio file in background
        background_tasks.add_task(cleanup_file, audio_path)
        
        return analysis
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Analysis failed: {str(e)}")

@app.post("/compare", response_model=ComparisonResult)
async def compare_organs(request: ComparisonRequest):
    """Compare timbre characteristics between two organ recordings"""
    try:
        comparison = await comparative_analyzer.compare_analyses(
            request.video_id_1, 
            request.video_id_2
        )
        return comparison
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Comparison failed: {str(e)}")

@app.get("/analyses")
async def get_analyses(organ_type: Optional[str] = None, limit: int = 50):
    """Get stored analyses with optional filtering"""
    # This will connect to Supabase to retrieve stored analyses
    pass

@app.get("/statistics")
async def get_statistics():
    """Get statistical overview including temperament and tuning analysis"""
    return await comparative_analyzer.get_statistics()

# NEW ENDPOINTS

@app.post("/upload-audio")
async def upload_audio_file(
    file: UploadFile = File(...),
    metadata: str = Form(...)
):
    """Upload and analyze an audio file"""
    try:
        # Parse metadata JSON
        metadata_dict = json.loads(metadata)

        # Process uploaded file
        analysis = await user_data_service.process_uploaded_file(file, metadata_dict)

        # Add to database
        analysis_id = await user_data_service.add_to_database(analysis)

        return {
            "analysis": analysis,
            "analysis_id": analysis_id,
            "message": "File uploaded and analyzed successfully"
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Upload failed: {str(e)}")

class ExternalLinkRequest(BaseModel):
    url: str
    metadata: Dict[str, Any] = {}

@app.post("/analyze-link")
async def analyze_external_link(request: ExternalLinkRequest):
    """Analyze an external audio/video link"""
    try:
        analysis = await user_data_service.process_external_link(request.url, request.metadata)
        analysis_id = await user_data_service.add_to_database(analysis)

        return {
            "analysis": analysis,
            "analysis_id": analysis_id,
            "message": "Link analyzed successfully"
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Link analysis failed: {str(e)}")

@app.get("/upload-guidelines")
async def get_upload_guidelines():
    """Get guidelines for uploading data"""
    return user_data_service.get_upload_guidelines()

@app.post("/discover-batch")
async def discover_and_analyze_batch(batch_size: int = 10):
    """Autonomously discover and analyze a batch of organ videos"""
    try:
        analyses = await autonomous_discovery.discover_and_analyze_batch(batch_size)

        return {
            "analyses": analyses,
            "count": len(analyses),
            "message": f"Successfully discovered and analyzed {len(analyses)} videos"
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Discovery failed: {str(e)}")

@app.get("/temperament-stats")
async def get_temperament_statistics():
    """Get detailed temperament and tuning statistics"""
    stats = await comparative_analyzer.get_statistics()

    return {
        "temperament_distribution": stats["temperament_distribution"],
        "tuning_distribution": stats["tuning_distribution"],
        "temperament_by_organ_type": stats["temperament_by_organ_type"],
        "tuning_by_organ_type": stats["tuning_by_organ_type"],
        "key_findings": stats["key_findings"]
    }

def cleanup_file(file_path: str):
    """Clean up temporary files"""
    try:
        if os.path.exists(file_path):
            os.remove(file_path)
    except Exception:
        pass

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
