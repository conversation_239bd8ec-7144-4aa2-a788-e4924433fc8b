from pydantic import BaseModel
from typing import Dict, List, Optional, Any
from datetime import datetime

class TimbreFeatures(BaseModel):
    """Detailed timbre characteristics of an organ recording"""
    
    # Spectral characteristics
    spectral_centroid: float  # "Brightness" of the sound
    spectral_rolloff: float   # High-frequency content
    spectral_bandwidth: float # Spread of frequencies
    spectral_contrast: List[float]  # Contrast across frequency bands
    
    # Harmonic analysis
    harmonic_ratios: List[float]  # Strength of 2nd, 3rd, 4th, etc. harmonics
    inharmonicity_index: float    # Deviation from perfect harmonic ratios
    fundamental_frequency: float  # Base frequency
    
    # MFCC (Mel-frequency cepstral coefficients) - timbral fingerprint
    mfcc_coefficients: List[float]  # 13 coefficients
    
    # Temporal characteristics
    attack_time: float        # Time to reach peak amplitude
    decay_time: float         # Time to decay to 50% amplitude
    sustain_level: float      # Sustained amplitude level
    
    # Advanced timbral features
    roughness: float          # Perceptual roughness
    brightness_index: float   # High-frequency energy ratio
    warmth_index: float       # Low-frequency energy ratio
    
    # Organ-specific features
    pipe_speech_clarity: float    # Attack transient characteristics
    wind_system_stability: float  # Amplitude stability over time
    room_resonance_factor: float  # Reverb and room acoustics influence

    # NEW: Temperament and tuning analysis
    temperament_type: str         # "equal", "just", "meantone", "well_tempered"
    temperament_confidence: float # Confidence in temperament classification
    interval_deviations: List[float]  # Deviations from equal temperament in cents
    tuning_reference: float       # Estimated A4 frequency
    tuning_confidence: float      # Confidence in tuning analysis
    is_432hz_tuning: bool        # True if tuned to A432
    is_440hz_tuning: bool        # True if tuned to A440
    reference_type: str          # "A432", "A440", "A415_baroque", etc.

class VideoMetadata(BaseModel):
    """YouTube video metadata"""
    title: str
    description: Optional[str] = None
    upload_date: Optional[str] = None
    duration: Optional[float] = None
    view_count: Optional[int] = None
    uploader: Optional[str] = None
    tags: Optional[List[str]] = None

class VideoAnalysis(BaseModel):
    """Complete analysis of a church organ video"""
    id: Optional[str] = None
    youtube_url: str
    title: str
    organ_type: str  # "historical", "modern", "restored"
    organ_age: Optional[int] = None
    restoration_date: Optional[int] = None
    location: Optional[str] = None
    church_name: Optional[str] = None
    
    timbre_features: TimbreFeatures
    metadata: Dict[str, Any]
    
    analysis_date: datetime = datetime.now()
    confidence_score: Optional[float] = None  # ML confidence in organ classification

class ComparisonResult(BaseModel):
    """Result of comparing two organ recordings"""
    video_1_id: str
    video_2_id: str
    
    # Overall similarity metrics
    overall_similarity: float  # 0-1 scale
    spectral_similarity: float
    harmonic_similarity: float
    temporal_similarity: float
    
    # Specific differences
    brightness_difference: float
    warmth_difference: float
    roughness_difference: float
    
    # Key distinguishing features
    key_differences: List[str]
    similarity_breakdown: Dict[str, float]
    
    # Statistical significance
    p_value: Optional[float] = None
    confidence_interval: Optional[List[float]] = None

class OrganClassification(BaseModel):
    """ML classification result for organ type"""
    predicted_type: str  # "historical", "modern", "restored"
    confidence: float
    feature_importance: Dict[str, float]
    
class StatisticalSummary(BaseModel):
    """Statistical overview of analyzed organs"""
    total_analyses: int
    historical_count: int
    modern_count: int
    restored_count: int
    
    # Average characteristics by type
    avg_brightness_historical: float
    avg_brightness_modern: float
    avg_brightness_restored: float
    
    avg_warmth_historical: float
    avg_warmth_modern: float
    avg_warmth_restored: float
    
    # Significant differences found
    significant_differences: List[Dict[str, Any]]
