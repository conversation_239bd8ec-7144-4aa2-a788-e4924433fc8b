import asyncio
import random
from typing import List, Dict, Optional
import yt_dlp
from datetime import datetime, timedelta
import re
import logging

from services.youtube_service import YouTubeService
from services.timbre_analyzer import TimbreAnalyzer
from models.analysis_models import VideoAnalysis

class AutonomousDiscoveryService:
    """Service for autonomously discovering and analyzing church organ videos"""
    
    def __init__(self):
        self.youtube_service = YouTubeService()
        self.timbre_analyzer = TimbreAnalyzer()
        self.processed_videos = set()  # Track processed video IDs
        
        # Search terms for finding organ videos
        self.search_terms = [
            "church organ bach",
            "pipe organ cathedral",
            "church organ classical music",
            "baroque organ music",
            "cathedral organ concert",
            "historic organ performance",
            "church organ hymns",
            "pipe organ recital",
            "organ music church service",
            "cathedral organ mass",
            "historic pipe organ",
            "church organ restoration",
            "baroque church organ",
            "cathedral pipe organ music"
        ]
        
        # Keywords that suggest historical vs modern organs
        self.historical_keywords = [
            "historic", "historical", "baroque", "renaissance", "medieval",
            "17th century", "18th century", "19th century", "original",
            "authentic", "period", "ancient", "old", "traditional"
        ]
        
        self.modern_keywords = [
            "modern", "contemporary", "new", "recent", "rebuilt", "renovated",
            "20th century", "21st century", "digital", "electronic"
        ]
        
        self.restoration_keywords = [
            "restored", "restoration", "renovated", "rebuilt", "refurbished",
            "reconstructed", "renewed", "revitalized"
        ]
    
    async def discover_and_analyze_batch(self, batch_size: int = 10) -> List[VideoAnalysis]:
        """Discover and analyze a batch of organ videos"""
        
        discovered_videos = await self._discover_videos(batch_size * 2)  # Get more than needed
        
        # Filter out already processed videos
        new_videos = [v for v in discovered_videos if v['id'] not in self.processed_videos]
        
        # Limit to batch size
        videos_to_process = new_videos[:batch_size]
        
        analyses = []
        for video_info in videos_to_process:
            try:
                analysis = await self._analyze_discovered_video(video_info)
                if analysis:
                    analyses.append(analysis)
                    self.processed_videos.add(video_info['id'])
                
                # Add delay to avoid rate limiting
                await asyncio.sleep(2)
                
            except Exception as e:
                logging.error(f"Failed to analyze video {video_info['id']}: {str(e)}")
                continue
        
        return analyses
    
    async def _discover_videos(self, count: int) -> List[Dict]:
        """Discover organ videos using YouTube search"""
        
        discovered_videos = []
        search_term = random.choice(self.search_terms)
        
        ydl_opts = {
            'quiet': True,
            'no_warnings': True,
            'extract_flat': True,
            'default_search': 'ytsearch',
        }
        
        try:
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                # Search for videos
                search_query = f"ytsearch{count}:{search_term}"
                search_results = ydl.extract_info(search_query, download=False)
                
                if 'entries' in search_results:
                    for entry in search_results['entries']:
                        if entry and 'id' in entry:
                            # Filter videos by duration (5-60 minutes for good analysis)
                            duration = entry.get('duration', 0)
                            if 300 <= duration <= 3600:  # 5 minutes to 1 hour
                                discovered_videos.append({
                                    'id': entry['id'],
                                    'title': entry.get('title', ''),
                                    'url': f"https://www.youtube.com/watch?v={entry['id']}",
                                    'duration': duration,
                                    'uploader': entry.get('uploader', ''),
                                    'upload_date': entry.get('upload_date', ''),
                                    'view_count': entry.get('view_count', 0)
                                })
        
        except Exception as e:
            logging.error(f"Failed to discover videos: {str(e)}")
        
        return discovered_videos
    
    async def _analyze_discovered_video(self, video_info: Dict) -> Optional[VideoAnalysis]:
        """Analyze a discovered video"""
        
        try:
            # Get full metadata
            metadata = await self.youtube_service.get_video_metadata(video_info['url'])
            
            # Classify organ type based on title and description
            organ_classification = self._classify_organ_type(
                video_info['title'], 
                metadata.get('description', '')
            )
            
            # Download and analyze audio
            audio_path = await self.youtube_service.download_audio(video_info['url'])
            timbre_features = await self.timbre_analyzer.analyze_audio(audio_path)
            
            # Extract location information
            location_info = self._extract_location_from_metadata(metadata)
            
            # Create analysis result
            analysis = VideoAnalysis(
                youtube_url=video_info['url'],
                title=video_info['title'],
                organ_type=organ_classification['type'],
                organ_age=organ_classification.get('estimated_age'),
                restoration_date=organ_classification.get('restoration_date'),
                location=location_info.get('location'),
                church_name=location_info.get('church_name'),
                timbre_features=timbre_features,
                metadata=metadata,
                confidence_score=organ_classification['confidence']
            )
            
            return analysis
            
        except Exception as e:
            logging.error(f"Failed to analyze video {video_info['id']}: {str(e)}")
            return None
    
    def _classify_organ_type(self, title: str, description: str) -> Dict:
        """Classify organ type based on title and description"""
        
        text = f"{title} {description}".lower()
        
        historical_score = sum(1 for keyword in self.historical_keywords if keyword in text)
        modern_score = sum(1 for keyword in self.modern_keywords if keyword in text)
        restoration_score = sum(1 for keyword in self.restoration_keywords if keyword in text)
        
        # Extract potential dates
        date_pattern = r'\b(1[4-9]\d{2}|20[0-2]\d)\b'  # Years 1400-2029
        dates = [int(match) for match in re.findall(date_pattern, text)]
        
        estimated_age = None
        restoration_date = None
        
        if dates:
            # Assume oldest date is build date, newest might be restoration
            dates.sort()
            estimated_age = dates[0]
            if len(dates) > 1 and dates[-1] > 1900:
                restoration_date = dates[-1]
        
        # Determine organ type
        if restoration_score > 0:
            organ_type = "restored"
            confidence = min(0.9, 0.5 + restoration_score * 0.1)
        elif historical_score > modern_score:
            organ_type = "historical"
            confidence = min(0.9, 0.5 + historical_score * 0.1)
        elif modern_score > 0:
            organ_type = "modern"
            confidence = min(0.9, 0.5 + modern_score * 0.1)
        else:
            # Default classification based on estimated age
            if estimated_age and estimated_age < 1950:
                organ_type = "historical"
                confidence = 0.6
            else:
                organ_type = "modern"
                confidence = 0.5
        
        return {
            'type': organ_type,
            'confidence': confidence,
            'estimated_age': estimated_age,
            'restoration_date': restoration_date
        }
    
    def _extract_location_from_metadata(self, metadata: Dict) -> Dict:
        """Extract location information from video metadata"""
        
        text = f"{metadata.get('title', '')} {metadata.get('description', '')}".lower()
        
        # Common European cities with historic organs
        cities = {
            'vienna': 'Vienna, Austria',
            'salzburg': 'Salzburg, Austria',
            'paris': 'Paris, France',
            'lyon': 'Lyon, France',
            'rome': 'Rome, Italy',
            'florence': 'Florence, Italy',
            'venice': 'Venice, Italy',
            'berlin': 'Berlin, Germany',
            'munich': 'Munich, Germany',
            'cologne': 'Cologne, Germany',
            'hamburg': 'Hamburg, Germany',
            'dresden': 'Dresden, Germany',
            'leipzig': 'Leipzig, Germany',
            'london': 'London, England',
            'cambridge': 'Cambridge, England',
            'oxford': 'Oxford, England',
            'amsterdam': 'Amsterdam, Netherlands',
            'utrecht': 'Utrecht, Netherlands',
            'prague': 'Prague, Czech Republic',
            'budapest': 'Budapest, Hungary',
            'stockholm': 'Stockholm, Sweden',
            'copenhagen': 'Copenhagen, Denmark',
            'oslo': 'Oslo, Norway'
        }
        
        # Church/cathedral keywords
        church_keywords = [
            'cathedral', 'church', 'basilica', 'abbey', 'monastery',
            'chapel', 'minster', 'dom', 'kirche', 'église', 'chiesa',
            'catedral', 'iglesia', 'kerk', 'kyrka'
        ]
        
        location = None
        church_name = None
        
        # Find city
        for city, full_location in cities.items():
            if city in text:
                location = full_location
                break
        
        # Extract church name (look for patterns like "St. Mary's Cathedral")
        for keyword in church_keywords:
            pattern = rf'(\w+(?:\s+\w+)*)\s+{keyword}'
            matches = re.findall(pattern, text, re.IGNORECASE)
            if matches:
                church_name = f"{matches[0]} {keyword.title()}"
                break
        
        return {
            'location': location,
            'church_name': church_name
        }
    
    async def run_continuous_discovery(self, interval_hours: int = 6, batch_size: int = 5):
        """Run continuous discovery process"""
        
        logging.info("Starting continuous organ video discovery...")
        
        while True:
            try:
                logging.info(f"Discovering and analyzing {batch_size} new videos...")
                analyses = await self.discover_and_analyze_batch(batch_size)
                
                logging.info(f"Successfully analyzed {len(analyses)} videos")
                
                # Here you would save the analyses to your database
                # await self._save_analyses_to_database(analyses)
                
                # Wait before next batch
                await asyncio.sleep(interval_hours * 3600)
                
            except Exception as e:
                logging.error(f"Error in continuous discovery: {str(e)}")
                await asyncio.sleep(300)  # Wait 5 minutes before retrying
