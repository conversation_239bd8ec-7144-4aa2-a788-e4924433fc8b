import librosa
import numpy as np
from scipy import signal, stats
from sklearn.preprocessing import StandardScaler
import soundfile as sf
from typing import List, Tuple, Dict
import warnings
warnings.filterwarnings('ignore')

from models.analysis_models import TimbreFeatures

class TimbreAnalyzer:
    """Advanced timbre analysis for church organ recordings"""
    
    def __init__(self, sample_rate: int = 22050):
        self.sample_rate = sample_rate
        self.hop_length = 512
        self.n_fft = 2048
        self.n_mfcc = 13
        
    async def analyze_audio(self, audio_path: str) -> TimbreFeatures:
        """Perform comprehensive timbre analysis on audio file"""
        
        # Load audio
        y, sr = librosa.load(audio_path, sr=self.sample_rate)
        
        # Ensure we have enough audio (at least 5 seconds)
        if len(y) < 5 * sr:
            raise ValueError("Audio too short for reliable analysis")
        
        # Extract all timbral features
        spectral_features = self._extract_spectral_features(y, sr)
        harmonic_features = self._extract_harmonic_features(y, sr)
        temporal_features = self._extract_temporal_features(y, sr)
        mfcc_features = self._extract_mfcc_features(y, sr)
        organ_specific_features = self._extract_organ_specific_features(y, sr)
        
        return TimbreFeatures(
            **spectral_features,
            **harmonic_features,
            **temporal_features,
            **mfcc_features,
            **organ_specific_features
        )
    
    def _extract_spectral_features(self, y: np.ndarray, sr: int) -> Dict:
        """Extract spectral characteristics"""
        
        # Compute spectral features
        spectral_centroids = librosa.feature.spectral_centroid(y=y, sr=sr, hop_length=self.hop_length)[0]
        spectral_rolloff = librosa.feature.spectral_rolloff(y=y, sr=sr, hop_length=self.hop_length)[0]
        spectral_bandwidth = librosa.feature.spectral_bandwidth(y=y, sr=sr, hop_length=self.hop_length)[0]
        spectral_contrast = librosa.feature.spectral_contrast(y=y, sr=sr, hop_length=self.hop_length)
        
        return {
            'spectral_centroid': float(np.mean(spectral_centroids)),
            'spectral_rolloff': float(np.mean(spectral_rolloff)),
            'spectral_bandwidth': float(np.mean(spectral_bandwidth)),
            'spectral_contrast': spectral_contrast.mean(axis=1).tolist()
        }
    
    def _extract_harmonic_features(self, y: np.ndarray, sr: int) -> Dict:
        """Extract harmonic content, temperament, and tuning analysis"""

        # Separate harmonic and percussive components
        y_harmonic, y_percussive = librosa.effects.hpss(y)

        # Estimate fundamental frequency
        f0, voiced_flag, voiced_probs = librosa.pyin(y_harmonic,
                                                    fmin=librosa.note_to_hz('C2'),
                                                    fmax=librosa.note_to_hz('C7'))

        # Remove NaN values and get median f0
        f0_clean = f0[~np.isnan(f0)]
        fundamental_freq = float(np.median(f0_clean)) if len(f0_clean) > 0 else 220.0

        # Analyze harmonic ratios
        harmonic_ratios = self._calculate_harmonic_ratios(y_harmonic, sr, fundamental_freq)

        # Calculate inharmonicity
        inharmonicity = self._calculate_inharmonicity(y_harmonic, sr, fundamental_freq)

        # NEW: Temperament and tuning analysis
        temperament_analysis = self._analyze_temperament(y_harmonic, sr)
        tuning_analysis = self._analyze_tuning_reference(y_harmonic, sr)

        return {
            'fundamental_frequency': fundamental_freq,
            'harmonic_ratios': harmonic_ratios,
            'inharmonicity_index': inharmonicity,
            'temperament_type': temperament_analysis['temperament_type'],
            'temperament_confidence': temperament_analysis['confidence'],
            'interval_deviations': temperament_analysis['interval_deviations'],
            'tuning_reference': tuning_analysis['reference_frequency'],
            'tuning_confidence': tuning_analysis['confidence'],
            'is_432hz_tuning': tuning_analysis['is_432hz'],
            'is_440hz_tuning': tuning_analysis['is_440hz']
        }
    
    def _extract_temporal_features(self, y: np.ndarray, sr: int) -> Dict:
        """Extract temporal characteristics (attack, decay, sustain)"""
        
        # Calculate RMS energy
        rms = librosa.feature.rms(y=y, hop_length=self.hop_length)[0]
        
        # Find attack time (time to reach 90% of peak)
        peak_idx = np.argmax(rms)
        peak_value = rms[peak_idx]
        attack_threshold = 0.9 * peak_value
        
        attack_time = 0.0
        for i in range(peak_idx):
            if rms[i] >= attack_threshold:
                attack_time = i * self.hop_length / sr
                break
        
        # Find decay time (time to decay to 50% of peak after peak)
        decay_threshold = 0.5 * peak_value
        decay_time = 0.0
        
        for i in range(peak_idx, len(rms)):
            if rms[i] <= decay_threshold:
                decay_time = (i - peak_idx) * self.hop_length / sr
                break
        
        # Calculate sustain level (average RMS in middle portion)
        middle_start = len(rms) // 3
        middle_end = 2 * len(rms) // 3
        sustain_level = float(np.mean(rms[middle_start:middle_end]))
        
        return {
            'attack_time': attack_time,
            'decay_time': decay_time,
            'sustain_level': sustain_level
        }
    
    def _extract_mfcc_features(self, y: np.ndarray, sr: int) -> Dict:
        """Extract MFCC coefficients for timbral fingerprinting"""
        
        mfccs = librosa.feature.mfcc(y=y, sr=sr, n_mfcc=self.n_mfcc, hop_length=self.hop_length)
        
        # Take mean across time for each coefficient
        mfcc_means = np.mean(mfccs, axis=1)
        
        return {
            'mfcc_coefficients': mfcc_means.tolist()
        }
    
    def _extract_organ_specific_features(self, y: np.ndarray, sr: int) -> Dict:
        """Extract features specific to organ analysis"""
        
        # Calculate roughness (based on amplitude modulation)
        roughness = self._calculate_roughness(y, sr)
        
        # Calculate brightness and warmth indices
        brightness_index = self._calculate_brightness_index(y, sr)
        warmth_index = self._calculate_warmth_index(y, sr)
        
        # Analyze pipe speech clarity (attack transient sharpness)
        pipe_speech_clarity = self._calculate_pipe_speech_clarity(y, sr)
        
        # Analyze wind system stability (amplitude variation)
        wind_stability = self._calculate_wind_stability(y, sr)
        
        # Estimate room resonance factor
        room_resonance = self._calculate_room_resonance(y, sr)
        
        return {
            'roughness': roughness,
            'brightness_index': brightness_index,
            'warmth_index': warmth_index,
            'pipe_speech_clarity': pipe_speech_clarity,
            'wind_system_stability': wind_stability,
            'room_resonance_factor': room_resonance
        }
    
    def _calculate_harmonic_ratios(self, y: np.ndarray, sr: int, f0: float) -> List[float]:
        """Calculate the strength of harmonics relative to fundamental"""
        
        # Compute magnitude spectrum
        D = np.abs(librosa.stft(y, hop_length=self.hop_length, n_fft=self.n_fft))
        freqs = librosa.fft_frequencies(sr=sr, n_fft=self.n_fft)
        
        # Find peaks near harmonic frequencies (up to 8th harmonic)
        harmonic_ratios = []
        
        for harmonic in range(2, 9):  # 2nd through 8th harmonic
            target_freq = f0 * harmonic
            
            # Find closest frequency bin
            freq_idx = np.argmin(np.abs(freqs - target_freq))
            
            # Average magnitude around this frequency
            window = 3  # ±3 bins
            start_idx = max(0, freq_idx - window)
            end_idx = min(len(freqs), freq_idx + window + 1)
            
            harmonic_magnitude = np.mean(D[start_idx:end_idx, :])
            
            # Normalize by fundamental (approximate)
            fundamental_idx = np.argmin(np.abs(freqs - f0))
            fundamental_magnitude = np.mean(D[max(0, fundamental_idx-window):
                                             min(len(freqs), fundamental_idx+window+1), :])
            
            ratio = float(harmonic_magnitude / (fundamental_magnitude + 1e-10))
            harmonic_ratios.append(ratio)
        
        return harmonic_ratios
    
    def _calculate_inharmonicity(self, y: np.ndarray, sr: int, f0: float) -> float:
        """Calculate inharmonicity coefficient"""
        
        # This is a simplified inharmonicity calculation
        # In practice, you'd want more sophisticated peak detection
        
        D = np.abs(librosa.stft(y, hop_length=self.hop_length, n_fft=self.n_fft))
        freqs = librosa.fft_frequencies(sr=sr, n_fft=self.n_fft)
        
        # Find actual vs. theoretical harmonic frequencies
        deviations = []
        
        for harmonic in range(2, 6):  # Check first few harmonics
            theoretical_freq = f0 * harmonic
            
            # Find peak near theoretical frequency
            search_range = int(0.1 * theoretical_freq / (sr / self.n_fft))  # ±10% search range
            center_idx = np.argmin(np.abs(freqs - theoretical_freq))
            
            start_idx = max(0, center_idx - search_range)
            end_idx = min(len(freqs), center_idx + search_range)
            
            if end_idx > start_idx:
                spectrum_slice = np.mean(D[start_idx:end_idx, :], axis=1)
                peak_idx = np.argmax(spectrum_slice) + start_idx
                actual_freq = freqs[peak_idx]
                
                deviation = abs(actual_freq - theoretical_freq) / theoretical_freq
                deviations.append(deviation)
        
        return float(np.mean(deviations)) if deviations else 0.0
    
    def _calculate_roughness(self, y: np.ndarray, sr: int) -> float:
        """Calculate perceptual roughness"""
        
        # Simplified roughness calculation based on amplitude modulation
        # Real implementation would use more sophisticated psychoacoustic models
        
        # Calculate amplitude envelope
        analytic_signal = signal.hilbert(y)
        amplitude_envelope = np.abs(analytic_signal)
        
        # Calculate modulation frequency content
        mod_spectrum = np.abs(np.fft.fft(amplitude_envelope))
        mod_freqs = np.fft.fftfreq(len(amplitude_envelope), 1/sr)
        
        # Roughness is related to modulation in 20-300 Hz range
        roughness_range = (mod_freqs >= 20) & (mod_freqs <= 300)
        roughness = float(np.sum(mod_spectrum[roughness_range]))
        
        return roughness / len(y)  # Normalize
    
    def _calculate_brightness_index(self, y: np.ndarray, sr: int) -> float:
        """Calculate brightness index (high-frequency energy ratio)"""
        
        # Compute power spectral density
        freqs, psd = signal.welch(y, sr, nperseg=1024)
        
        # Define high-frequency range (above 2kHz for organs)
        high_freq_mask = freqs >= 2000
        low_freq_mask = freqs < 2000
        
        high_freq_energy = np.sum(psd[high_freq_mask])
        total_energy = np.sum(psd)
        
        return float(high_freq_energy / (total_energy + 1e-10))
    
    def _calculate_warmth_index(self, y: np.ndarray, sr: int) -> float:
        """Calculate warmth index (low-frequency energy ratio)"""
        
        freqs, psd = signal.welch(y, sr, nperseg=1024)
        
        # Define low-frequency range (below 500Hz for organs)
        low_freq_mask = freqs <= 500
        total_energy = np.sum(psd)
        low_freq_energy = np.sum(psd[low_freq_mask])
        
        return float(low_freq_energy / (total_energy + 1e-10))
    
    def _calculate_pipe_speech_clarity(self, y: np.ndarray, sr: int) -> float:
        """Analyze pipe speech characteristics (attack transient clarity)"""
        
        # Calculate onset strength
        onset_envelope = librosa.onset.onset_strength(y=y, sr=sr, hop_length=self.hop_length)
        
        # Find onset peaks
        onsets = librosa.onset.onset_detect(onset_envelope=onset_envelope, 
                                           sr=sr, hop_length=self.hop_length)
        
        if len(onsets) == 0:
            return 0.0
        
        # Analyze sharpness of onsets (higher values = clearer pipe speech)
        onset_sharpness = []
        
        for onset in onsets[:10]:  # Analyze first 10 onsets
            if onset < len(onset_envelope) - 5:
                # Calculate slope of onset
                slope = np.diff(onset_envelope[onset:onset+5])
                onset_sharpness.append(np.max(slope))
        
        return float(np.mean(onset_sharpness)) if onset_sharpness else 0.0
    
    def _calculate_wind_stability(self, y: np.ndarray, sr: int) -> float:
        """Analyze wind system stability (amplitude consistency)"""
        
        # Calculate RMS in overlapping windows
        window_size = sr // 4  # 0.25 second windows
        hop_size = sr // 8     # 50% overlap
        
        rms_values = []
        for i in range(0, len(y) - window_size, hop_size):
            window = y[i:i + window_size]
            rms_values.append(np.sqrt(np.mean(window**2)))
        
        if len(rms_values) < 2:
            return 1.0
        
        # Stability is inverse of coefficient of variation
        cv = np.std(rms_values) / (np.mean(rms_values) + 1e-10)
        stability = 1.0 / (1.0 + cv)  # Higher values = more stable
        
        return float(stability)
    
    def _calculate_room_resonance(self, y: np.ndarray, sr: int) -> float:
        """Estimate room resonance characteristics"""
        
        # Calculate reverberation time estimate using energy decay
        # This is a simplified approach
        
        # Reverse the signal and calculate energy decay
        y_reversed = y[::-1]
        
        # Calculate cumulative energy
        energy = np.cumsum(y_reversed**2)
        energy = energy[::-1]  # Reverse back
        
        # Normalize
        energy = energy / energy[0]
        
        # Find RT60 (time for 60dB decay)
        # Look for point where energy drops to 1/1000 of original (60dB)
        target_energy = 0.001
        
        rt60_samples = len(energy)
        for i, e in enumerate(energy):
            if e <= target_energy:
                rt60_samples = i
                break
        
        rt60_time = rt60_samples / sr
        
        # Normalize to typical range for churches (1-8 seconds)
        room_factor = min(rt60_time / 8.0, 1.0)
        
        return float(room_factor)

    def _analyze_temperament(self, y: np.ndarray, sr: int) -> Dict:
        """Analyze temperament type (equal vs. historical temperaments)"""

        # Extract chromatic scale frequencies from the audio
        chroma = librosa.feature.chroma_stft(y=y, sr=sr, hop_length=self.hop_length)

        # Find the most prominent notes
        chroma_mean = np.mean(chroma, axis=1)
        prominent_notes = np.argsort(chroma_mean)[-6:]  # Top 6 notes

        # Calculate interval ratios between prominent notes
        interval_deviations = []
        temperament_scores = {
            'equal': 0.0,
            'just': 0.0,
            'meantone': 0.0,
            'well_tempered': 0.0
        }

        # Define expected interval ratios for different temperaments (in cents)
        equal_temp_intervals = [0, 100, 200, 300, 400, 500, 600, 700, 800, 900, 1000, 1100]
        just_intervals = [0, 112, 204, 316, 386, 498, 590, 702, 814, 884, 996, 1088]
        meantone_intervals = [0, 117, 193, 310, 386, 503, 579, 697, 773, 890, 966, 1083]

        # Calculate deviations from each temperament
        for i in range(len(prominent_notes) - 1):
            for j in range(i + 1, len(prominent_notes)):
                note1, note2 = prominent_notes[i], prominent_notes[j]
                interval_semitones = abs(note2 - note1)

                if interval_semitones < len(equal_temp_intervals):
                    # Compare with equal temperament
                    equal_deviation = abs(interval_semitones * 100 - equal_temp_intervals[interval_semitones])
                    temperament_scores['equal'] += 1.0 / (1.0 + equal_deviation / 10.0)

                    # Compare with just intonation
                    if interval_semitones < len(just_intervals):
                        just_deviation = abs(interval_semitones * 100 - just_intervals[interval_semitones])
                        temperament_scores['just'] += 1.0 / (1.0 + just_deviation / 10.0)

                    # Compare with meantone
                    if interval_semitones < len(meantone_intervals):
                        meantone_deviation = abs(interval_semitones * 100 - meantone_intervals[interval_semitones])
                        temperament_scores['meantone'] += 1.0 / (1.0 + meantone_deviation / 10.0)

                interval_deviations.append(float(equal_deviation))

        # Determine most likely temperament
        best_temperament = max(temperament_scores, key=temperament_scores.get)
        confidence = temperament_scores[best_temperament] / sum(temperament_scores.values()) if sum(temperament_scores.values()) > 0 else 0.0

        return {
            'temperament_type': best_temperament,
            'confidence': float(confidence),
            'interval_deviations': interval_deviations[:12],  # Limit to 12 intervals
            'temperament_scores': temperament_scores
        }

    def _analyze_tuning_reference(self, y: np.ndarray, sr: int) -> Dict:
        """Analyze tuning reference frequency (A4 = 432Hz vs 440Hz vs others)"""

        # Extract pitch information
        f0, voiced_flag, voiced_probs = librosa.pyin(y,
                                                    fmin=librosa.note_to_hz('C2'),
                                                    fmax=librosa.note_to_hz('C7'))

        # Remove NaN values
        f0_clean = f0[~np.isnan(f0)]

        if len(f0_clean) == 0:
            return {
                'reference_frequency': 440.0,
                'confidence': 0.0,
                'is_432hz': False,
                'is_440hz': False,
                'tuning_deviation': 0.0
            }

        # Find A notes in the recording
        a_frequencies = []

        for freq in f0_clean:
            # Calculate which note this frequency corresponds to
            # A4 = 440Hz in equal temperament
            note_number = 12 * np.log2(freq / 440.0) + 69  # MIDI note number
            note_in_octave = note_number % 12

            # Check if this is an A note (note 9 in chromatic scale, 0=C)
            if abs(note_in_octave - 9) < 0.5:  # Within 50 cents of A
                octave = int((note_number - 9) / 12) + 4  # Calculate octave
                # Normalize to A4 frequency
                a4_freq = freq / (2 ** (octave - 4))
                a_frequencies.append(a4_freq)

        if len(a_frequencies) == 0:
            # No clear A notes found, estimate from other notes
            median_freq = np.median(f0_clean)
            # Estimate A4 from median frequency (rough approximation)
            estimated_a4 = 440.0  # Default fallback
        else:
            estimated_a4 = np.median(a_frequencies)

        # Calculate deviations from common tuning standards
        deviation_432 = abs(estimated_a4 - 432.0)
        deviation_440 = abs(estimated_a4 - 440.0)
        deviation_415 = abs(estimated_a4 - 415.0)  # Baroque tuning
        deviation_466 = abs(estimated_a4 - 466.0)  # High baroque

        # Determine closest tuning standard (within 10 Hz tolerance)
        tolerance = 10.0
        is_432hz = deviation_432 < tolerance
        is_440hz = deviation_440 < tolerance
        is_415hz = deviation_415 < tolerance
        is_466hz = deviation_466 < tolerance

        # Calculate confidence based on consistency of A note frequencies
        if len(a_frequencies) > 1:
            confidence = 1.0 / (1.0 + np.std(a_frequencies))
        else:
            confidence = 0.5  # Medium confidence for estimated values

        # Determine the most likely reference
        min_deviation = min(deviation_432, deviation_440, deviation_415, deviation_466)
        if min_deviation == deviation_432:
            reference_type = "A432"
        elif min_deviation == deviation_440:
            reference_type = "A440"
        elif min_deviation == deviation_415:
            reference_type = "A415_baroque"
        else:
            reference_type = "A466_high_baroque"

        return {
            'reference_frequency': float(estimated_a4),
            'confidence': float(confidence),
            'is_432hz': is_432hz,
            'is_440hz': is_440hz,
            'is_415hz': is_415hz,
            'is_466hz': is_466hz,
            'reference_type': reference_type,
            'tuning_deviation': float(min_deviation),
            'a_note_count': len(a_frequencies)
        }
