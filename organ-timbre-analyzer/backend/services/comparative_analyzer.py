import numpy as np
from scipy import stats
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.preprocessing import StandardScaler
from typing import Dict, List, Tuple, Optional
import asyncio

from models.analysis_models import ComparisonResult, TimbreFeatures, StatisticalSummary

class ComparativeAnalyzer:
    """Service for comparing organ timbre characteristics and statistical analysis"""
    
    def __init__(self):
        self.scaler = StandardScaler()
        
    async def compare_analyses(self, video_id_1: str, video_id_2: str) -> ComparisonResult:
        """Compare two organ analyses and return detailed comparison"""
        
        # In a real implementation, you'd fetch these from database
        # For now, this is a placeholder structure
        
        # This would fetch actual TimbreFeatures from database
        features_1 = await self._get_features_by_id(video_id_1)
        features_2 = await self._get_features_by_id(video_id_2)
        
        if not features_1 or not features_2:
            raise ValueError("Could not find analysis data for one or both videos")
        
        # Calculate various similarity metrics
        overall_similarity = self._calculate_overall_similarity(features_1, features_2)
        spectral_similarity = self._calculate_spectral_similarity(features_1, features_2)
        harmonic_similarity = self._calculate_harmonic_similarity(features_1, features_2)
        temporal_similarity = self._calculate_temporal_similarity(features_1, features_2)
        
        # Calculate specific differences
        brightness_diff = abs(features_1.brightness_index - features_2.brightness_index)
        warmth_diff = abs(features_1.warmth_index - features_2.warmth_index)
        roughness_diff = abs(features_1.roughness - features_2.roughness)
        
        # Identify key differences
        key_differences = self._identify_key_differences(features_1, features_2)
        
        # Create detailed similarity breakdown
        similarity_breakdown = {
            'spectral_centroid': self._calculate_feature_similarity(
                features_1.spectral_centroid, features_2.spectral_centroid
            ),
            'harmonic_content': harmonic_similarity,
            'attack_characteristics': self._calculate_feature_similarity(
                features_1.attack_time, features_2.attack_time
            ),
            'wind_stability': self._calculate_feature_similarity(
                features_1.wind_system_stability, features_2.wind_system_stability
            ),
            'room_acoustics': self._calculate_feature_similarity(
                features_1.room_resonance_factor, features_2.room_resonance_factor
            )
        }
        
        # Statistical significance testing
        p_value, confidence_interval = self._calculate_statistical_significance(
            features_1, features_2
        )
        
        return ComparisonResult(
            video_1_id=video_id_1,
            video_2_id=video_id_2,
            overall_similarity=overall_similarity,
            spectral_similarity=spectral_similarity,
            harmonic_similarity=harmonic_similarity,
            temporal_similarity=temporal_similarity,
            brightness_difference=brightness_diff,
            warmth_difference=warmth_diff,
            roughness_difference=roughness_diff,
            key_differences=key_differences,
            similarity_breakdown=similarity_breakdown,
            p_value=p_value,
            confidence_interval=confidence_interval
        )
    
    async def get_statistics(self) -> Dict:
        """Get statistical overview including temperament and tuning analysis"""

        # This would query the database for all analyses
        # For now, returning enhanced placeholder data with temperament/tuning stats

        return {
            'total_analyses': 0,
            'historical_count': 0,
            'modern_count': 0,
            'restored_count': 0,

            # Traditional timbral statistics
            'avg_brightness_historical': 0.0,
            'avg_brightness_modern': 0.0,
            'avg_brightness_restored': 0.0,
            'avg_warmth_historical': 0.0,
            'avg_warmth_modern': 0.0,
            'avg_warmth_restored': 0.0,

            # NEW: Temperament statistics
            'temperament_distribution': {
                'equal': {'count': 0, 'percentage': 0.0},
                'just': {'count': 0, 'percentage': 0.0},
                'meantone': {'count': 0, 'percentage': 0.0},
                'well_tempered': {'count': 0, 'percentage': 0.0}
            },

            # NEW: Tuning reference statistics
            'tuning_distribution': {
                'A432': {'count': 0, 'percentage': 0.0},
                'A440': {'count': 0, 'percentage': 0.0},
                'A415_baroque': {'count': 0, 'percentage': 0.0},
                'A466_high_baroque': {'count': 0, 'percentage': 0.0},
                'other': {'count': 0, 'percentage': 0.0}
            },

            # Cross-analysis: temperament by organ type
            'temperament_by_organ_type': {
                'historical': {
                    'equal': 0, 'just': 0, 'meantone': 0, 'well_tempered': 0
                },
                'restored': {
                    'equal': 0, 'just': 0, 'meantone': 0, 'well_tempered': 0
                },
                'modern': {
                    'equal': 0, 'just': 0, 'meantone': 0, 'well_tempered': 0
                }
            },

            # Cross-analysis: tuning by organ type
            'tuning_by_organ_type': {
                'historical': {
                    'A432': 0, 'A440': 0, 'A415_baroque': 0, 'A466_high_baroque': 0, 'other': 0
                },
                'restored': {
                    'A432': 0, 'A440': 0, 'A415_baroque': 0, 'A466_high_baroque': 0, 'other': 0
                },
                'modern': {
                    'A432': 0, 'A440': 0, 'A415_baroque': 0, 'A466_high_baroque': 0, 'other': 0
                }
            },

            # Research insights
            'key_findings': [
                # These would be generated based on actual data
                # "Historical organs show 73% meantone temperament vs 12% equal temperament",
                # "Modern organs are 89% equal temperament with A440 tuning",
                # "Restored organs show mixed temperament patterns"
            ],

            'significant_differences': []
        }
    
    def _calculate_overall_similarity(self, features_1: TimbreFeatures, features_2: TimbreFeatures) -> float:
        """Calculate overall similarity between two sets of features"""
        
        # Convert features to vectors for comparison
        vector_1 = self._features_to_vector(features_1)
        vector_2 = self._features_to_vector(features_2)
        
        # Calculate cosine similarity
        similarity = cosine_similarity([vector_1], [vector_2])[0][0]
        
        # Convert to 0-1 scale (cosine similarity is -1 to 1)
        return float((similarity + 1) / 2)
    
    def _calculate_spectral_similarity(self, features_1: TimbreFeatures, features_2: TimbreFeatures) -> float:
        """Calculate similarity of spectral characteristics"""
        
        spectral_features_1 = [
            features_1.spectral_centroid,
            features_1.spectral_rolloff,
            features_1.spectral_bandwidth,
            features_1.brightness_index,
            features_1.warmth_index
        ]
        
        spectral_features_2 = [
            features_2.spectral_centroid,
            features_2.spectral_rolloff,
            features_2.spectral_bandwidth,
            features_2.brightness_index,
            features_2.warmth_index
        ]
        
        # Add spectral contrast features
        spectral_features_1.extend(features_1.spectral_contrast)
        spectral_features_2.extend(features_2.spectral_contrast)
        
        # Calculate normalized similarity
        return self._calculate_vector_similarity(spectral_features_1, spectral_features_2)
    
    def _calculate_harmonic_similarity(self, features_1: TimbreFeatures, features_2: TimbreFeatures) -> float:
        """Calculate similarity of harmonic content"""
        
        harmonic_features_1 = [
            features_1.fundamental_frequency,
            features_1.inharmonicity_index
        ]
        harmonic_features_1.extend(features_1.harmonic_ratios)
        
        harmonic_features_2 = [
            features_2.fundamental_frequency,
            features_2.inharmonicity_index
        ]
        harmonic_features_2.extend(features_2.harmonic_ratios)
        
        return self._calculate_vector_similarity(harmonic_features_1, harmonic_features_2)
    
    def _calculate_temporal_similarity(self, features_1: TimbreFeatures, features_2: TimbreFeatures) -> float:
        """Calculate similarity of temporal characteristics"""
        
        temporal_features_1 = [
            features_1.attack_time,
            features_1.decay_time,
            features_1.sustain_level,
            features_1.pipe_speech_clarity,
            features_1.wind_system_stability
        ]
        
        temporal_features_2 = [
            features_2.attack_time,
            features_2.decay_time,
            features_2.sustain_level,
            features_2.pipe_speech_clarity,
            features_2.wind_system_stability
        ]
        
        return self._calculate_vector_similarity(temporal_features_1, temporal_features_2)
    
    def _calculate_vector_similarity(self, vector_1: List[float], vector_2: List[float]) -> float:
        """Calculate similarity between two feature vectors"""
        
        if len(vector_1) != len(vector_2):
            raise ValueError("Vectors must have same length")
        
        # Normalize vectors
        v1 = np.array(vector_1)
        v2 = np.array(vector_2)
        
        # Handle zero vectors
        if np.allclose(v1, 0) or np.allclose(v2, 0):
            return 0.0
        
        # Calculate cosine similarity
        similarity = np.dot(v1, v2) / (np.linalg.norm(v1) * np.linalg.norm(v2))
        
        # Convert to 0-1 scale
        return float((similarity + 1) / 2)
    
    def _calculate_feature_similarity(self, value_1: float, value_2: float) -> float:
        """Calculate similarity between two individual feature values"""
        
        # Normalize difference to 0-1 scale
        max_val = max(abs(value_1), abs(value_2), 1.0)  # Avoid division by zero
        difference = abs(value_1 - value_2) / max_val
        
        return float(1.0 - min(difference, 1.0))
    
    def _features_to_vector(self, features: TimbreFeatures) -> List[float]:
        """Convert TimbreFeatures to a numerical vector for comparison"""
        
        vector = [
            features.spectral_centroid,
            features.spectral_rolloff,
            features.spectral_bandwidth,
            features.fundamental_frequency,
            features.inharmonicity_index,
            features.attack_time,
            features.decay_time,
            features.sustain_level,
            features.roughness,
            features.brightness_index,
            features.warmth_index,
            features.pipe_speech_clarity,
            features.wind_system_stability,
            features.room_resonance_factor
        ]
        
        # Add spectral contrast and harmonic ratios
        vector.extend(features.spectral_contrast)
        vector.extend(features.harmonic_ratios)
        vector.extend(features.mfcc_coefficients)
        
        return vector
    
    def _identify_key_differences(self, features_1: TimbreFeatures, features_2: TimbreFeatures) -> List[str]:
        """Identify the most significant differences between two analyses"""
        
        differences = []
        
        # Define thresholds for significant differences
        brightness_threshold = 0.1
        warmth_threshold = 0.1
        roughness_threshold = 0.2
        attack_threshold = 0.05  # 50ms
        stability_threshold = 0.1
        
        # Check brightness difference
        brightness_diff = abs(features_1.brightness_index - features_2.brightness_index)
        if brightness_diff > brightness_threshold:
            brighter = "first" if features_1.brightness_index > features_2.brightness_index else "second"
            differences.append(f"Significant brightness difference: {brighter} organ is notably brighter")
        
        # Check warmth difference
        warmth_diff = abs(features_1.warmth_index - features_2.warmth_index)
        if warmth_diff > warmth_threshold:
            warmer = "first" if features_1.warmth_index > features_2.warmth_index else "second"
            differences.append(f"Significant warmth difference: {warmer} organ has more low-frequency content")
        
        # Check roughness difference
        roughness_diff = abs(features_1.roughness - features_2.roughness)
        if roughness_diff > roughness_threshold:
            rougher = "first" if features_1.roughness > features_2.roughness else "second"
            differences.append(f"Roughness difference: {rougher} organ sounds rougher/more textured")
        
        # Check attack time difference
        attack_diff = abs(features_1.attack_time - features_2.attack_time)
        if attack_diff > attack_threshold:
            faster = "first" if features_1.attack_time < features_2.attack_time else "second"
            differences.append(f"Attack speed difference: {faster} organ has faster pipe speech")
        
        # Check wind system stability
        stability_diff = abs(features_1.wind_system_stability - features_2.wind_system_stability)
        if stability_diff > stability_threshold:
            more_stable = "first" if features_1.wind_system_stability > features_2.wind_system_stability else "second"
            differences.append(f"Wind system difference: {more_stable} organ has more stable wind pressure")
        
        # Check harmonic content differences
        harmonic_diff = np.mean([abs(h1 - h2) for h1, h2 in 
                                zip(features_1.harmonic_ratios, features_2.harmonic_ratios)])
        if harmonic_diff > 0.1:
            differences.append("Significant differences in harmonic content structure")
        
        # Check room acoustics
        room_diff = abs(features_1.room_resonance_factor - features_2.room_resonance_factor)
        if room_diff > 0.2:
            more_reverb = "first" if features_1.room_resonance_factor > features_2.room_resonance_factor else "second"
            differences.append(f"Room acoustics difference: {more_reverb} organ recorded in more reverberant space")
        
        return differences
    
    def _calculate_statistical_significance(self, features_1: TimbreFeatures, features_2: TimbreFeatures) -> Tuple[Optional[float], Optional[List[float]]]:
        """Calculate statistical significance of differences"""
        
        # This is a simplified approach - in practice you'd need multiple samples
        # For now, return placeholder values
        
        # Convert features to vectors
        vector_1 = self._features_to_vector(features_1)
        vector_2 = self._features_to_vector(features_2)
        
        # Perform t-test (this is not statistically valid with n=1, but shows the structure)
        try:
            # In real implementation, you'd have multiple samples for each organ type
            # t_stat, p_value = stats.ttest_ind(samples_1, samples_2)
            
            # For now, return placeholder
            p_value = 0.05  # Placeholder
            confidence_interval = [0.0, 1.0]  # Placeholder
            
            return p_value, confidence_interval
            
        except Exception:
            return None, None
    
    async def _get_features_by_id(self, video_id: str) -> Optional[TimbreFeatures]:
        """Fetch TimbreFeatures from database by video ID"""
        
        # This would query Supabase database
        # For now, return None to indicate not implemented
        return None
