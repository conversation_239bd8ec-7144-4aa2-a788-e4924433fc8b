import os
import tempfile
import uuid
from typing import Optional, Dict, Any
import soundfile as sf
import librosa
from fastapi import UploadFile
import httpx
from urllib.parse import urlparse

from services.timbre_analyzer import <PERSON>bre<PERSON>nalyzer
from services.youtube_service import YouTubeService
from models.analysis_models import VideoAnalysis, TimbreFeatures

class UserDataService:
    """Service for handling user-uploaded audio data and external links"""
    
    def __init__(self):
        self.timbre_analyzer = TimbreAnalyzer()
        self.youtube_service = YouTubeService()
        self.temp_dir = tempfile.gettempdir()
        
        # Supported audio formats
        self.supported_formats = {'.wav', '.mp3', '.flac', '.ogg', '.m4a', '.aac'}
        
        # Maximum file size (50MB)
        self.max_file_size = 50 * 1024 * 1024
    
    async def process_uploaded_file(
        self, 
        file: UploadFile, 
        metadata: Dict[str, Any]
    ) -> VideoAnalysis:
        """Process an uploaded audio file"""
        
        # Validate file
        self._validate_uploaded_file(file)
        
        # Save uploaded file temporarily
        file_id = str(uuid.uuid4())
        temp_path = os.path.join(self.temp_dir, f"upload_{file_id}.wav")
        
        try:
            # Read and convert audio file
            audio_data = await file.read()
            
            # Convert to WAV format for analysis
            converted_path = await self._convert_audio_to_wav(audio_data, temp_path)
            
            # Analyze audio
            timbre_features = await self.timbre_analyzer.analyze_audio(converted_path)
            
            # Create analysis result
            analysis = VideoAnalysis(
                youtube_url=f"user_upload_{file_id}",
                title=metadata.get('title', file.filename or 'User Upload'),
                organ_type=metadata.get('organ_type', 'unknown'),
                organ_age=metadata.get('organ_age'),
                restoration_date=metadata.get('restoration_date'),
                location=metadata.get('location'),
                church_name=metadata.get('church_name'),
                timbre_features=timbre_features,
                metadata={
                    'source': 'user_upload',
                    'filename': file.filename,
                    'file_size': len(audio_data),
                    'content_type': file.content_type,
                    **metadata
                }
            )
            
            return analysis
            
        finally:
            # Clean up temporary files
            if os.path.exists(temp_path):
                os.remove(temp_path)
            if 'converted_path' in locals() and os.path.exists(converted_path):
                os.remove(converted_path)
    
    async def process_external_link(
        self, 
        url: str, 
        metadata: Dict[str, Any]
    ) -> VideoAnalysis:
        """Process an external audio/video link"""
        
        parsed_url = urlparse(url)
        
        # Check if it's a YouTube URL
        if 'youtube.com' in parsed_url.netloc or 'youtu.be' in parsed_url.netloc:
            return await self._process_youtube_link(url, metadata)
        
        # Check if it's a direct audio file link
        elif any(url.lower().endswith(ext) for ext in self.supported_formats):
            return await self._process_direct_audio_link(url, metadata)
        
        # Try to download and analyze as audio
        else:
            return await self._process_generic_link(url, metadata)
    
    async def _process_youtube_link(self, url: str, metadata: Dict[str, Any]) -> VideoAnalysis:
        """Process a YouTube link using existing YouTube service"""
        
        # Download and extract audio
        audio_path = await self.youtube_service.download_audio(url)
        
        try:
            # Get video metadata
            video_metadata = await self.youtube_service.get_video_metadata(url)
            
            # Analyze audio
            timbre_features = await self.timbre_analyzer.analyze_audio(audio_path)
            
            # Merge user metadata with video metadata
            combined_metadata = {**video_metadata, **metadata, 'source': 'youtube_user_provided'}
            
            # Create analysis result
            analysis = VideoAnalysis(
                youtube_url=url,
                title=metadata.get('title', video_metadata.get('title', 'YouTube Video')),
                organ_type=metadata.get('organ_type', 'unknown'),
                organ_age=metadata.get('organ_age'),
                restoration_date=metadata.get('restoration_date'),
                location=metadata.get('location'),
                church_name=metadata.get('church_name'),
                timbre_features=timbre_features,
                metadata=combined_metadata
            )
            
            return analysis
            
        finally:
            # Clean up downloaded file
            if os.path.exists(audio_path):
                os.remove(audio_path)
    
    async def _process_direct_audio_link(self, url: str, metadata: Dict[str, Any]) -> VideoAnalysis:
        """Process a direct link to an audio file"""
        
        file_id = str(uuid.uuid4())
        temp_path = os.path.join(self.temp_dir, f"download_{file_id}")
        
        try:
            # Download the audio file
            async with httpx.AsyncClient() as client:
                response = await client.get(url)
                response.raise_for_status()
                
                # Check file size
                if len(response.content) > self.max_file_size:
                    raise ValueError(f"File too large: {len(response.content)} bytes")
                
                # Save to temporary file
                with open(temp_path, 'wb') as f:
                    f.write(response.content)
            
            # Convert to WAV for analysis
            wav_path = temp_path + '.wav'
            converted_path = await self._convert_audio_to_wav_file(temp_path, wav_path)
            
            # Analyze audio
            timbre_features = await self.timbre_analyzer.analyze_audio(converted_path)
            
            # Create analysis result
            analysis = VideoAnalysis(
                youtube_url=url,
                title=metadata.get('title', os.path.basename(url)),
                organ_type=metadata.get('organ_type', 'unknown'),
                organ_age=metadata.get('organ_age'),
                restoration_date=metadata.get('restoration_date'),
                location=metadata.get('location'),
                church_name=metadata.get('church_name'),
                timbre_features=timbre_features,
                metadata={
                    'source': 'direct_audio_link',
                    'original_url': url,
                    'file_size': len(response.content),
                    **metadata
                }
            )
            
            return analysis
            
        finally:
            # Clean up temporary files
            for path in [temp_path, temp_path + '.wav']:
                if os.path.exists(path):
                    os.remove(path)
    
    async def _process_generic_link(self, url: str, metadata: Dict[str, Any]) -> VideoAnalysis:
        """Try to process a generic link that might contain audio"""
        
        # This could be expanded to handle various streaming services,
        # SoundCloud, Bandcamp, etc. using appropriate extractors
        
        raise ValueError(f"Unsupported link type: {url}")
    
    def _validate_uploaded_file(self, file: UploadFile) -> None:
        """Validate uploaded file"""
        
        if not file.filename:
            raise ValueError("No filename provided")
        
        # Check file extension
        file_ext = os.path.splitext(file.filename)[1].lower()
        if file_ext not in self.supported_formats:
            raise ValueError(f"Unsupported file format: {file_ext}")
        
        # Note: File size validation happens during processing
        # since we need to read the file to check its size
    
    async def _convert_audio_to_wav(self, audio_data: bytes, output_path: str) -> str:
        """Convert audio data to WAV format"""
        
        # Save raw data to temporary file first
        temp_input = output_path + '.tmp'
        with open(temp_input, 'wb') as f:
            f.write(audio_data)
        
        try:
            return await self._convert_audio_to_wav_file(temp_input, output_path)
        finally:
            if os.path.exists(temp_input):
                os.remove(temp_input)
    
    async def _convert_audio_to_wav_file(self, input_path: str, output_path: str) -> str:
        """Convert audio file to WAV format using librosa"""
        
        try:
            # Load audio with librosa (handles many formats)
            y, sr = librosa.load(input_path, sr=22050)  # Standardize sample rate
            
            # Save as WAV
            sf.write(output_path, y, sr)
            
            return output_path
            
        except Exception as e:
            raise ValueError(f"Failed to convert audio file: {str(e)}")
    
    async def add_to_database(self, analysis: VideoAnalysis) -> str:
        """Add analysis to the database and return the ID"""
        
        # This would integrate with your database service
        # For now, return a placeholder ID
        
        analysis_id = str(uuid.uuid4())
        
        # Here you would:
        # 1. Save to Supabase/PostgreSQL
        # 2. Update statistics
        # 3. Trigger any post-processing
        
        return analysis_id
    
    def get_upload_guidelines(self) -> Dict[str, Any]:
        """Get guidelines for users uploading data"""
        
        return {
            'supported_formats': list(self.supported_formats),
            'max_file_size_mb': self.max_file_size // (1024 * 1024),
            'recommended_duration': '5-60 minutes',
            'recommended_quality': 'High quality recording with minimal background noise',
            'metadata_fields': {
                'required': ['organ_type'],
                'optional': [
                    'title', 'organ_age', 'restoration_date', 
                    'location', 'church_name', 'recording_date',
                    'performer', 'composer', 'piece_title'
                ]
            },
            'organ_types': ['historical', 'restored', 'modern'],
            'tips': [
                'Record in a quiet environment',
                'Use single stops or simple registrations for best analysis',
                'Include metadata for research value',
                'Longer recordings provide more accurate analysis'
            ]
        }
